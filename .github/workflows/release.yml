name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - run: npx changelogithub gh release
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
