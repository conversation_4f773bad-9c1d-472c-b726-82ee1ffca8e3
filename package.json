{"name": "astro-theme-vitesse", "type": "module", "version": "1.3.1", "packageManager": "npm@10.x", "description": "A minimal, SEO-friendly portfolio and blog theme for Astro, supports Vue and UnoCSS.", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/kieranwv/)", "license": "MIT", "keywords": ["hbin", "<PERSON><PERSON>", "astro-theme-vitesse", "astro", "blog", "starter", "template"], "engines": {"node": ">=v18.17.1 || >=v20.3.0 || >=21"}, "scripts": {"prepare": "simple-git-hooks", "dev": "astro dev --host", "build": "astro build", "preview": "astro preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "bumpp", "new-post": "node scripts/new-post.js", "generate-tags": "node scripts/generate-tags.js", "process-fragments": "node scripts/process-fragments.js"}, "dependencies": {"@astrojs/mdx": "^4.2.4", "@astrojs/rss": "^4.0.11", "@astrojs/sitemap": "^3.3.0", "@astrojs/vue": "^5.0.10", "@unocss/reset": "66.1.0-beta.11", "@vue/compiler-sfc": "^3.5.17", "astro": "^5.6.2", "json-schema-traverse": "^1.0.0", "nprogress": "^0.2.0", "unocss": "66.1.0-beta.11", "vite": "^6.0.0", "vue": "^3.5.13"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@iconify/json": "^2.2.327", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@vueuse/core": "^13.1.0", "bumpp": "^10.1.0", "eslint": "^9.24.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-format": "^1.0.1", "lint-staged": "^15.5.1", "lodash-es": "^4.17.21", "prettier-plugin-astro": "^0.14.1", "simple-git-hooks": "^2.12.1", "typescript": "^5.8.3"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": "npm run lint:fix"}}