---
import { fade } from 'astro:transitions'
import { ViewTransitions } from 'astro:transitions'
import BaseHead from '@/components/BaseHead.astro'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import ScrollToTop from '@/components/ScrollToTop.vue'

const { ...head } = Astro.props
---

<!doctype html>
<html lang="en">
  <head>
    <BaseHead {...head} />
    <ViewTransitions />
  </head>
  <body class="bg-main text-main min-h-screen font-sans w-full bg-dot">
    <Header client:load />
    <main
      class="grow max-w-3xl mx-auto sm:pt-36 pt-26 pb-16 px-6 relative"
      transition:animate={fade({ duration: '0.4s' })}
    >
      <slot />
      <ScrollToTop client:load />
      <Footer />
    </main>
  </body>
</html>
