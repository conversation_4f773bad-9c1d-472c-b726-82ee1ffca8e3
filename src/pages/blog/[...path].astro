---
import BaseLayout from '@/layouts/BaseLayout.astro'
import ListPosts from '@/components/ListPosts.vue'
import siteConfig from '@/site-config'
import { getPosts } from '@/utils/posts'

export async function getStaticPaths() {
  const paths = siteConfig.page.blogLinks.map((nav) => {
    const href = nav.href.replace('/blog', '')
    return {
      params: {
        path: href === '' ? undefined : href.replace(/^\/+|\/+$/g, ''),
      },
    }
  })
  return paths
}

const { path } = Astro.params

const posts = await getPosts(path)

function activeLink(pathname: string) {
  return Astro.url.pathname.replace(/\/+/g, '/').replace(/\/$/, '') === pathname
}
---

<BaseLayout title="Blog" description="List of all the blog posts." pageNav={true} pageOperate={true}>
  <div class="flex flex-col gap-2 sm:flex-row sm:gap-4 flex-wrap mb-8">
    {
      siteConfig.page.blogLinks.map((nav) => (
        <a
          aria-label={nav.text}
          class={`nav-link text-3xl font-bold ${activeLink(nav.href) ? 'opacity-80' : 'opacity-30 hover:opacity-50'}`}
          href={nav.href}
        >
          {nav.text}
        </a>
      ))
    }
  </div>
  <ListPosts list={posts} />
</BaseLayout>
