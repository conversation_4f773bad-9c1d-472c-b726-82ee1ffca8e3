---
import BaseLayout from '@/layouts/BaseLayout.astro'
import { type CollectionPosts } from '@/types'
import { getPosts } from '@/utils/posts'

export async function getStaticPaths() {
  const posts = await getPosts()
  return posts.map((post) => {
    return {
      params: { slug: post.slug },
      props: {
        post,
      },
    }
  })
}

type Props = { post: CollectionPosts }

const { post } = Astro.props
const { title, image, description, date, duration, tag } = post.data

const { Content } = await post.render()

function getDate(date: string) {
  return new Date(date).toISOString()
}
---

<BaseLayout title={title} description={description} pageType="article">
  <article class="prose">
    <h1>{title}</h1>
    <p op-50>
      {date && <time datetime={getDate(date)}>{date.split(',')}</time>}
      {duration && <span>· {duration}</span>}
      {tag && <span>· {tag}</span>}
    </p>
    {
      image && (
        <p>
          <img width="640" height="360" src={image.src} alt={image.alt || ''} />
        </p>
      )
    }
    <Content />
  </article>
</BaseLayout>
