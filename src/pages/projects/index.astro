---
import { projectData } from './data'
import BaseLayout from '@/layouts/BaseLayout.astro'
import ListProjects from '@/components/ListProjects.vue'
---

<BaseLayout title="Projects" description="List of projects that I am proud of">
  <h1 class="mb-10 text-title">Projects</h1>
  <div mb-16 sm:mb-24>
    {
      projectData.length > 0 && (
        <div>
          {projectData.map((i) => (
            <div mb-10>
              <h2 class="select-none relative h20 pointer-events-none">
                <span class="text-3.2em color-transparent font-bold text-stroke-1.5 text-stroke-hex-aaa op35 dark:op20 absolute top-0">
                  {i.title}
                </span>
              </h2>
              <ListProjects list={i.projects} />
            </div>
          ))}
        </div>
      )
    }
  </div>
</BaseLayout>
