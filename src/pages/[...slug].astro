---
import { getCollection } from 'astro:content'
import BaseLayout from '@/layouts/BaseLayout.astro'
import type { CollectionPages } from '@/types'

export async function getStaticPaths() {
  const pages = await getCollection('pages')
  return pages.map((page) => {
    return {
      params: { slug: page.slug },
      props: { page },
    }
  })
}

type Props = { page: CollectionPages }

const { page } = Astro.props
const { title, description, image } = page.data

const { Content } = await page.render()
---

<BaseLayout title={title} description={description}>
  <article class="prose">
    <h1>{title}</h1>
    {
      image && (
        <p>
          <img width="640" height="360" src={image.src} alt={image.alt || ''} />
        </p>
      )
    }
    <Content />
  </article>
</BaseLayout>
