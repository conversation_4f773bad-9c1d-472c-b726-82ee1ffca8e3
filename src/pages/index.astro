---
import BaseLayout from '@/layouts/BaseLayout.astro'
import siteConfig from '@/site-config'
---

<BaseLayout description={siteConfig.description}>
  <article class="prose">
    <h1 class="text-title">H<PERSON><PERSON></h1>
    <p>
      Hello, I'm a front-end engineer passionate about exploring, building, and defining myself in the world of code.
    </p>

    <p class="mt-4"><b>Core Expertise:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-vue text-xl"></i>
        <b class="font-semibold">Vue</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-nuxt-icon text-xl"></i>
        <b class="font-semibold">Nuxt</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-nodejs-icon text-xl"></i>
        <b class="font-semibold">Node.js</b>
      </span>
    </div>

    <p class="mt-4"><b>Architecture & Paradigms:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-set-split text-xl"></i>
        <b class="font-semibold">Micro-Frontends</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-puzzle-outline text-xl text-blue-600"></i>
        <b class="font-semibold">Low-Code Platforms</b>
      </span>
    </div>

    <p class="mt-4"><b>Build Tools:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-vitejs text-xl"></i>
        <b class="font-semibold">Vite</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-webpack text-xl"></i>
        <b class="font-semibold">Webpack</b>
      </span>
    </div>

    <p class="mt-4"><b>DevOps:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-docker-icon text-xl"></i>
        <b class="font-semibold">Docker</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-nginx text-xl"></i>
        <b class="font-semibold">Nginx</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-git text-xl"></i>
        <b class="font-semibold">CI/CD</b>
      </span>
    </div>

    <p class="mt-4"><b>Multi-platform Development:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-cellphone-link text-xl"></i>
        <b class="font-semibold">uni-app</b>
      </span>
    </div>

    <p class="mt-4"><b>AI Exploration:</b></p>
    <div class="flex flex-wrap gap-x-4 gap-y-2">
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-robot-outline text-xl text-green-600"></i>
        <b class="font-semibold">AI (RAG, Agents)</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-python text-xl"></i>
        <b class="font-semibold">Python</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-logos-langchain text-xl"></i>
        <b class="font-semibold">LangChain</b>
      </span>
      <span class="flex items-center gap-x-1.5">
        <i class="i-mdi-api text-xl"></i>
        <b class="font-semibold">LLM APIs</b>
      </span>
    </div>

    <p class="mt-6">
      I enjoy the process of rapid learning, adept at enhancing my workflow with AI-powered IDEs or extensions like <b
        >Cursor</b
      > and <b>Roo Code</b>, and leveraging <b>LLM APIs</b> for custom solutions. Currently, I'm actively learning the <b
        class="inline-flex items-center gap-x-1.5 align-bottom"><i class="i-logos-react text-lg"></i>React</b
      > ecosystem to further broaden my technical horizons.
    </p>

    <p>You can find my list of projects <a target="_blank" href="/projects">here</a>.</p>

    <hr class="hr-line" />
    <p>Find me on</p>
    <p class="flex gap-x-4 gap-y-2 flex-wrap">
      {
        siteConfig.socialLinks.map((link) => (
          <a aria-label={link.text} href={link.href} target="_blank" class="prose-link">
            <i class:list={[link.icon]} />
            {link.text}
          </a>
        ))
      }
    </p>
    <p>
      If you have any questions, feel free to reach out via email:
      <a prose-link aria-label={siteConfig.email} href={`mailto:${siteConfig.email}`}>
        {siteConfig.email}
      </a>.
    </p>
  </article>
</BaseLayout>
