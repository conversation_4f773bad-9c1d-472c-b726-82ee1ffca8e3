html.dark .astro-code,
html.dark .astro-code span {
  color: var(--shiki-dark) !important;
  background-color: #161b22 !important;
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

html.dark {
  color-scheme: dark;
}

html:not(.dark) {
  color-scheme: light;
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: #888;
  opacity: 0.75;
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

img {
  --at-apply: rd-1.5;
}

article {
  --at-apply: sm: min-h-38 min-h-28;
}

.prose-link i {
  --at-apply: text-sm mr-1;
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 1;
}

::view-transition-new(root) {
  z-index: 2147483646;
}

html.dark::view-transition-old(root) {
  z-index: 2147483646;
}
html.dark::view-transition-new(root) {
  z-index: 1;
}
