---
title: "前端开发知识点大杂烩 1-4 第一部分（218-255条）"
description: "深入探讨 a 标签下载、Element UI 表格与选择器高级用法、CSS 伪类、Vue 动态绑定及生命周期等实战技巧"
date: "2021-10-15"
lang: "zh-CN"
category: "tech"
tag: "frontend"
tags: ["Element UI", "CSS", "Vue", "JavaScript", "DOM", "前端实战"]
featured: true
---

# 前端开发知识点大杂烩 1-4 第一部分（218-255条）

> 这是知识点大杂烩系列的第四部分，聚焦于 Element UI 常用组件的深入使用、CSS 技巧以及 Vue 开发中的常见问题与解决方案。

## 218. a 标签 download 跨域问题

当 `<a>` 标签的 `href` 指向一个跨域的 URL 时，`download` 属性会失效，浏览器会尝试导航到该 URL 而不是下载它。这是出于安全考虑。

**解决方案**：
1.  **后端代理**：通过后端服务转发文件流，将跨域资源变为同域资源。
2.  **前端转换**：使用 `fetch` 获取资源，转换为 Blob 或 Base64 URL，然后赋值给 `href`。这要求目标服务器设置了���确的 CORS 策略（`Access-Control-Allow-Origin`）。

![a 标签 download](./images/WEBRESOURCE18fad360e39515a952a380dd532d4bfc截图.png)

## 219. el-table 固定列边框问题

在 `el-table` 中，当同时设置 `border` 和 `fixed`（固定列）时，固定列的右侧可能会出现双边框或边框缺失的问题。

**解决方案**：通过 CSS 覆盖 Element UI 的样式，精确调整边框。
```css
/* 修复固定列右侧双边框问题 */
.el-table__fixed-right {
  right: 0 !important;
}
.el-table__fixed {
  height: 100% !important;
}
```
![el-table 固定列](./images/WEBRESOURCE192553e8cf9781b41868815648ef3509截图.png)

## 220. el-select allow-create 与 default-first-option

-   `allow-create`: 允许用户创建新条目，适用于需要用户输入自定义值的场景。
-   `default-first-option`: 在输入框按下回车时，选择第一个匹配项。

当两者结合使用时，如果输入的内容与现有选项不匹配，按回车会创建一个新条目；如果匹配，则会选中第一个匹配项。

![el-select allow-create](./images/WEBRESOURCE0e731dc6d62a0dbfd6046cc61f6e1f20截图.png)

## 221. el-select 自定义下拉选项内容

通过 `el-option` 的默认插槽，可以自定义下拉选项的显示内容，例如添加图标、多行文本等。

```html
<el-select v-model="value">
  <el-option
    v-for="item in options"
    :key="item.value"
    :label="item.label"
    :value="item.value">
    <span style="float: left">{{ item.label }}</span>
    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
  </el-option>
</el-select>
```
![el-select 自定义模板](./images/WEBRESOURCE0e84ac809a5bb9a28c5f92aa8bc95d0a截图.png)

## 222. el-select 虚拟列表处理大数据

当 `el-select` 的选项数量非常多（上千甚至上万）时，一次性渲染所有 `el-option` 会导致严重的性能问题。

**解决方案**：
1.  **使用支持虚拟滚动的第三方组件**：如 `el-select-v2` (Element Plus) 或其他虚拟列表库。
2.  **分页加载/远程搜索**：结合 `remote` 属性，在用户滚动或输入时动态加载部分数据。

![el-select 虚拟列表](./images/WEBRESOURCE0c96d0657efc6b02d856a83e75aead42image.png)

## 223. el-select 远程搜索

设置 `remote` 属性为 `true`，并提供一个 `remote-method`，可以在用户输入时，从服务器动态搜索数据。

```html
<el-select
  v-model="value"
  filterable
  remote
  :remote-method="remoteMethod"
  :loading="loading">
  ...
</el-select>
```
![el-select 远程搜索](./images/WEBRESOURCE0c312a4d72239e4d60f1d0594006a326截图.png)

## 224. el-select 多选 `collapse-tags`

在多选模式下，当选中项过多时，输入框会变得很长。使用 `collapse-tags` 属性可以将多个选中项合并为一段文字（如“+3”）。

![el-select collapse-tags](./images/WEBRESOURCE0c1e1d72711d2df18de732fd53e265bc截图.png)

## 225. el-select `clearable` 触发 `change` 事件

当 `clearable` 属性为 `true` 时，点击清空按钮，会将 `v-model` 的值设为 `undefined` 或 `''` (取决于版本和多选状态)，并触发 `change` 事件。

![el-select clearable](./images/WEBRESOURCE0be4c9ddf9abb2ac4825ac668eb48f77截图.png)

## 226. el-select 动态禁用选项

可以通过在 `el-option` 上绑定 `:disabled` 属性来动态地禁用某个选项。

```html
<el-option
  v-for="item in options"
  :key="item.value"
  :label="item.label"
  :value="item.value"
  :disabled="item.disabled">
</el-option>
```
![el-option disabled](./images/WEBRESOURCE0bb4555ef71546b8e26446f66cd37f76截图.png)

## 227. el-select 绑定值为对象

`el-select` 的 `v-model` 默认绑定的是 `el-option` 的 `value` 属性。如果希望绑定整个对象，需要将 `value-key` 属性设置为一个唯一的键（如 `id`）。

```html
<el-select v-model="selectedUser" value-key="id">
  <el-option
    v-for="user in users"
    :key="user.id"
    :label="user.name"
    :value="user">
  </el-option>
</el-select>
```
![el-select value-key](./images/WEBRESOURCE08239619ae5390c4bbe508789bf82ffcimage.png)

## 228. CSS 伪类 `:empty`

`:empty` 伪类选择器用于选中没有任何子元素（包括文本节点）的元素。

```css
/* 选中内容为空的 div，并给它一个高度和背景色，使其可见 */
div:empty {
  height: 20px;
  background-color: lightgray;
}
```
![CSS :empty](./images/WEBRESOURCE0221f98f7e3a40e8f6f20c05526900cb截图.png)

## 229. Vue 动态绑定 `ref`

在 `v-for` 循环中，可以通过一个函数来动态设置 `ref`，以便将 DOM 节点或组件实例存储在对象或 Map 中，而不是默认的数组中。

```html
<div v-for="item in list" :ref="el => setItemRef(el, item.id)"></div>
```
```javascript
const itemRefs = {};
const setItemRef = (el, id) => {
  if (el) {
    itemRefs[id] = el;
  }
};
```
![Vue 动态 ref](./images/WEBRESOURCE1132d3099492a731e0ed7c7829fa0c8fimage.png)

## 230. Vue `deactivated` 生命周期

`deactivated` 是 `<keep-alive>` 组件包裹的组件特有的生命周期钩子。当组件被缓存切换到后台时触发。与之对应的是 `activated`，在组件被重新激活时触发。

![deactivated hook](./images/WEBRESOURCE105c5112aea3390e4ad49fb87f096fd5截图.png)

## 231. `el-dialog` 嵌套 `el-table` 样式问题

当 `el-dialog` 中嵌套 `el-table` 时，如果表格数据过多出现滚动条，可能会有样式错乱。通常需要手动设置表格的高度或最大高度。

```html
<el-dialog>
  <el-table :data="tableData" height="400px">
    ...
  </el-table>
</el-dialog>
```
![dialog table height](./images/WEBRESOURCE101ba0877d6c04b6978207cc0c9c2e2c截图.png)

## 232. `el-form` `validateField` 方法

`validateField` 用于对单个表单项进行校验。它接收一个或多个字段名作为参数。

```javascript
this.$refs.myForm.validateField('username', (errorMessage) => {
  if (!errorMessage) {
    // 验证通过
  }
});
```
![validateField](./images/WEBRESOURCE0ffc983aae0653694f12f0e9072efd1c截图.png)

## 233. `el-tree` 默认展开和选中

-   `default-expanded-keys`: 设置默认展开的节点的 key 的数组。
-   `default-checked-keys`: 设置默认选中的节点的 key 的数组。

![el-tree default expand](./images/WEBRESOURCE0f4bf197bd98524bad01ba1c125799fc截图.png)

## 234. `el-date-picker` `picker-options`

`picker-options` 属性用于配置日期选择器的行为，例如禁用特定日期、设置快捷选项等。

```javascript
pickerOptions: {
  disabledDate(time) {
    return time.getTime() > Date.now();
  },
  shortcuts: [{...}]
}
```
![picker-options](./images/WEBRESOURCE0f474edf1dedc3673a5d9719ac87aaa4截图.png)

## 235. `el-tooltip` `disabled` 属性

通过 `:disabled` 属性可以动态地控制 `el-tooltip` 是否显示。

![el-tooltip disabled](./images/WEBRESOURCE0e9ca838edc13f460235b16c34eb8866截图.png)

## 236. `el-upload` `on-exceed` 钩子

当设置了 `limit` 属性后，如果用户选择的文件数量超过了限制，`on-exceed` 钩子会被触发。

![on-exceed hook](./images/WEBRESOURCE0db89a3d6a1f8f91d52e111458fb1853截图.png)

## 237. `el-pagination` 布局 `layout`

`layout` 属性用于控制分页组件的显示内容和顺序，如 `total`, `sizes`, `prev`, `pager`, `next`, `jumper`。

![pagination layout](./images/WEBRESOURCE0da5bf8ffe1b6b25167ca8b82cf550af截图.png)

## 238. `el-badge` `is-dot` 属性

设置 `is-dot` 属性为 `true`，可以将徽标显示为一个红点，常用于消息提示。

![badge is-dot](./images/WEBRESOURCE0d7f1660edbdfdbf6116ab3bab645626截图.png)

## 239. `el-alert` `closable` 属性

`closable` 属性用于控制警告提示是否可以手动关闭。

![alert closable](./images/WEBRESOURCE0d1cbef9803a923c725de46c587cb52b截图.png)

## 240. `el-tabs` `before-leave` 钩子

`before-leave` 钩子在标签页切换之前执行，可以用于阻止切换。它返回一个 `Promise` 或 `boolean`。

![tabs before-leave](./images/WEBRESOURCE0b5d45cda6dab183171a95364a3595a9截图.png)

## 241. `el-input-number` 精度 `precision`

`precision` 属性用于控制计数器允许的精度（小数位数）。

![input-number precision](./images/WEBRESOURCE0b1d80ef10044a6a1d53130633ba637b截图.png)

## 242. `el-cascader` `show-all-levels`

默认情况下，级联选择器只显示选中项的最后一级。设置 `show-all-levels` 为 `false` 可以改变这个行为。

![cascader show-all-levels](./images/WEBRESOURCE0ac92fa3ee4c76a82d750ee9ada4a5d1截图.png)

## 243. `el-color-picker` `show-alpha`

`show-alpha` 属性用于启用颜色选择器的透明度选择功能。

![color-picker show-alpha](./images/WEBRESOURCE0a347b8da64a1c6ca9290b8f7cf41066截图.png)

## 244. `el-rate` `show-text`

`show-text` 属性可以在评分组件右侧显示辅助文字，内容由 `texts` 数组提供。

![rate show-text](./images/WEBRESOURCE0a1e6087a5a4e961d841d82a428aa3eb截图.png)

## 245. `el-slider` `range` 属性

设置 `range` 属性为 `true`，可以开启范围选择模式。

![slider range](./images/WEBRESOURCE09bf5a2c902937486958aa54f604c74b截图.png)

## 246. `el-switch` `active-color` 和 `inactive-color`

这两个属性用于自定义开关在开启和关闭状态下的背景颜色。

![switch colors](./images/WEBRESOURCE08af773c7fd7fa3e41b7d269e1a56cc8截图.png)

## 247. `el-timeline` `placement`

`placement` 属性可以设置时间线节点内容相对于时间戳的位置，可选值为 `top` 或 `bottom`。

![timeline placement](./images/WEBRESOURCE08333719a0969c1717e45d9e7591cc8f截图.png)

## 248. `el-calendar` `range`

`range` 属性用于设置日历的显示范围，接收一个包含起始日期和结束日期的数组。

![calendar range](./images/WEBRESOURCE078630f400b8ec73f8c0ee3546ce2a61截图.png)

## 249. `el-image` `lazy` 和 `scroll-container`

-   `lazy`: 开启图片懒加载。
-   `scroll-container`: 指定一个滚动容器，当图片滚动到该容器的可视区域时才加载。

![image lazy](./images/WEBRESOURCE072e3d84fe9bd1a81d67d0f07e94eb4b截图.png)

## 250. `el-backtop` `target`

`target` 属性用于指定触发滚动的对象，通常是一个 CSS 选择器。

![backtop target](./images/WEBRESOURCE070e34615dee614f033df590d2880bf5截图.png)

## 251. `el-drawer` `direction`

`direction` 属性用于控制抽屉的打开方向，可选值为 `rtl` (从右到左), `ltr`, `ttb`, `btt`。

![drawer direction](./images/WEBRESOURCE06ef908e7438e4e8b9df512eb96aabc6截图.png)

## 252. `el-popconfirm` `icon`

`icon` 和 `icon-color` 属性用于自定义 Popconfirm 气泡确认框中的图标和颜色。

![popconfirm icon](./images/WEBRESOURCE06a3602af065ca00895e4b085e19edb8截图.png)

## 253. `el-card` `shadow`

`shadow` 属性用于设置卡片的阴影显示时机，可选值为 `always`, `hover`, `never`。

![card shadow](./images/WEBRESOURCE066c7022e7b8d62a643d382a8a09b76a截图.png)

## 254. `el-carousel` `type`

`type` 属性可以改变走马灯的样式，例如设置为 `card` 可以实现卡片化效果。

![carousel type](./images/WEBRESOURCE04e021660333149e33f28a7c83816669image.png)

## 255. `el-collapse` `accordion`

`accordion` 属性为 `true` 时，开启手风琴模式，每次只能展开一个面板。

![collapse accordion](./images/WEBRESOURCE04ac208f3f881192b5525f70dba395a7image.png)
