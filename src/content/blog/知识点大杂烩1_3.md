# JavaScript知识点大杂烩
> 转换自Word文档
> 转换时间: 2024年

## 201、 const 定义的对象（引用类型）的值可以改变，定义的基本数据类型不可以改变。

主要是指针（栈内存地址）没有发生改变。 const obj = { }, obj.name = 'HB'  ,  首先先在栈内存中开辟

一块空间存放 obj（ { } ） 的地址，然后地址指向堆内存空间，存放该地址的值，obj.name 改变的是堆内存

空间，栈内存还是没有改变（指针的指向不变）。

![图片](images/WEBRESOURCE8bf0d415010761531d69f59ab8cbb349截图.png)

### let的一个特性是禁止在同一个作用域下重复声明，所以以下代码会报错 :

![图片](images/WEBRESOURCEaaf3d7a00c05e1ab940499582dc7158f截图.png)

const  定义的变量  必须初始化，不然报错； var 和 let 则不用 。

![图片](images/WEBRESOURCE9cf9273506349f748bf14142ca8c8122截图.png)

## 202、  refs 和 document.xxx  获取 dom 的 区别 ：

1）、 ref 用来给 元素 或 子组件  注册引用信息，如果是元素，就是引用指向绑定的元素的DOM，如果是子组件，那就是引用指向子组件的实例

用 ref， 不同的组件互相隔离，不存在命名冲突

精准匹配，在vue 创建dom 的过程就直接赋予，不需二次查询，理论更快。

2）、用原生的方法 document.querySelector / document.getElementById 等方式：  每次用都需要做查询，比较消耗DOM节点的获取，匹配class类名时可能因为多个同名而选不到想要的DOM节点

vue中 v-for 循环时 动态设置 ref 会有问题 ！！

（1）、

![图片](images/WEBRESOURCE91f3f5c320f8acce51d89a49da44b349截图.png)

![图片](images/WEBRESOURCE1752a42d1dd4661a1f71d9ac61b05544截图.png)

![图片](images/WEBRESOURCEc07553cb06b8ead46c8db1a4cc7297ad截图.png)

（2）、

![图片](images/WEBRESOURCE0161d6f430efbbb2da447b112618260c截图.png)

![图片](images/WEBRESOURCEcd668fad35a17eaaf2191f18ea04c6c9截图.png)

## 203、 a 标签 的使用

### _target 属性 ：

需求： 点击一个链接，如果这个链接浏览器已经打开过，则刷新已经打开的链接窗口；如果这个链接没有打开过，则使用新窗口打开这个链接页面。

只需要 设置target属性值和href属性值一样就好了

![图片](images/WEBRESOURCE5185c791d43db2fb5d15d7fcafe11d7d截图.png)

默认 _blank 是打开一个新的页面

### download 属性：

![图片](images/WEBRESOURCE9ae839ce9bda0fee22dba7035b24fa52截图.png)

![图片](images/WEBRESOURCE99dce61b435b1ac594f77538ab98b525截图.png)

上面的截图也就是下面的意思。

![图片](images/WEBRESOURCE65036e77768b71e06739fe88b8d2bdc1截图.png)

图片转 base64 （dataURL） 或者 Blob (   new Blob( [ url ] )   )+ File Reader 对象

base64 （a-zA-Z0-9+/    共 64 个字符），每3个字节一组，共24个二进制位。24个二进制位分为4组，每组6个二进制位。每组前加两个00，扩展成32个二进制位，即4个字节。

![图片](images/WEBRESOURCEc561981ad039e39f351501e334438a0b截图.png)

所以。base64编码 最后是 变大了 （因为3个字节变成了4个字节。）

![图片](images/WEBRESOURCE9749f9ba4d643970953e36b1d7ec8d00截图.png)

以后的文件下载可以这么参考着来实现（都是请求接口的）     前端自己实现有点麻烦（各种插件尝试，又不熟悉用法+没有现成的完整实例）

后面就直接调接口就完事了！！！！！！

![图片](images/WEBRESOURCE993cc3a989afe0d3f38fece15332e09b截图.png)

![图片](images/WEBRESOURCE41ba893f929915474a4e768c3dc119a1截图.png)

图片转 base64 方法    （就不会有跨域问题了 / 减少 http 请求） （图片的话就不请求接口了，直接把url地址转成base64的，丢到new Blob() 里就行了，或者用html2canvas插件也行）

（1）、 Blob和FileReader 对象

![图片](images/WEBRESOURCE12972303599df41eda209c0b6ffe9df0截图.png)

![图片](images/WEBRESOURCEb19eaf31b1b6e69d3175659bc8e6b41d截图.png)

![图片](images/WEBRESOURCE524da122f9cf82568b5c1700fb9987df截图.png)

URL.createObjectURL 对象 专门将 blob 转为 DOMString类型的 url 用的api，  （该 API 不会有 跨域问题 前端自己实现的）

浏览器直接打开文件

![图片](images/WEBRESOURCE53f70c8a54e7790b40c86aafcfb0d8c2截图.png)

![图片](images/WEBRESOURCE36cbffa583d2e57f56d130a1b7733dd7截图.png)

### 注意：：

![图片](images/WEBRESOURCEb1db3b703fa8e1bb6cffc9db001e9da2截图.png)

（2）、 canvas.toDataURL()方法

![图片](images/WEBRESOURCE82a183250e30b40009cff9a4ff7d78a0截图.png)

![图片](images/WEBRESOURCE18cc2ad26bf479b0f33c147138e845e8截图.png)

![图片](images/WEBRESOURCE3bcc88e552d24c51c6689e9586f2e298截图.png)

![图片](images/WEBRESOURCEc05c7e6b84f4d0514b904da5838c3d0d截图.png)

创建一个 img 标签

![图片](images/WEBRESOURCE8e05e7e009e65a547541373854158d9d截图.png)

url 转 file

url 先转 blob， 再用 new File( [ blob ], filename, { type: 'xxx' } ) 传 blob 转成 file 对象

![图片](images/WEBRESOURCE3fa87e4c54f6b33cbb80957a14a51820截图.png)

![图片](images/WEBRESOURCE7b49e7f410b8ff94b47c5416a1c657bb截图.png)

// 使用

imgUrlToFile(imgUrl, function(file) {

console.log(file); // 文件格式

});

![图片](images/WEBRESOURCE7400125ed16d661216738b569d897228截图.png)

这样子每次都多一个图片请求了

## 204、

![图片](images/WEBRESOURCE980e409fca98869dc375860a76c02bed截图.png)

-S： --save     生产环境

-D: --dev         开发环境

## 205、 切分支的时候 node_modules 依赖是不会跟着切的

很多时候依赖装好了，但是依然运行项目会报错（而且其他人可以，其他电脑环境可以，就你本地跑的时候有问题），

这个时候就要考虑 依赖的版本问题 导致某些 api不兼容 然后报错的 ！！！

所以一些 主要的依赖要手动锁死版本，防止不兼容报错 ！！！！！！

（1）、最常见的就是 package.json 中直接写死版本号 （不用 ^ 和 ~ 指定版本号区间）;

（2）、或者拿别人的带 package.lock.json (npm安装)     /    带 yarn.lock (yarn 安装)    文件拷贝过来，删除自己的该文件，再重新装包 （npm / yarn 会根据锁定文件的包版本进行安装）

（3）、使用 npm 提供的 shrinlwrap 命令 锁定依赖包

![图片](images/WEBRESOURCEc37d358d65f9e9a5670023cec718773c截图.png)

符号^：表示主版本固定的情况下，可更新最新版。例如：vuex: "^3.1.3"，3.1.3及其以上的3.x.x都是满足的

符号~：表示次版本固定的情况下，可更新最新版。如：vuex: "~3.1.3"，3.1.3及其以上的3.1.x都是满足的

无符号：无符号表示固定版本号，例如：vuex: "3.1.3"，此时一定是安装3.1.3版本

![图片](images/WEBRESOURCEbcd3b9220066a8cf99ac07db2d90a7b7截图.png)

npm cache clean --fource 强制清楚本地缓存

### Yarn :

![图片](images/WEBRESOURCE9e77e7b78eaf9408b1f9d274a1e73ff2截图.png)

像npm一样，yarn使用本地缓存。与npm不同的是，yarn无需互联网连接就能安装本地缓存的依赖项，它提供了离线模式。最主要的是比 npm 快多了

Pnpm:    用的都是缓存来的 (多个项目的包，指向的都是同一个,都是软链) 软链接类似windows系统的快捷方式；

![图片](images/WEBRESOURCE46c97784e5688383b4828f470d351d19截图.png)

还有一个特点，就是磁盘空间利用高效（Fast, disk space efficient package manager）

支持 monorepo ，monorepo 的宗旨就是用一个 git 仓库来管理多个子项目，而不是一个子项目一个git 仓库 （git submodule 形式，但是多个子项目还是单独是一个git仓库）

monorepo  依赖统一管理（避免重复安装依赖）、代码共享（减少重复代码和组件的开发和维护）、代码可重用性（同一个仓库中的不同项目可以重复使用） 包相互引用

pnpm install： 安装所有的依赖   （其他用法跟npm类似， update / uninstall / ..）

![图片](images/WEBRESOURCE5c70ff7c0058b7d292b44212c359d73e截图.png)

pnpm i  装包报错

![图片](images/WEBRESOURCEeaa4afcb3c3e948b0144e9b23e1e90ed截图.png)

因为设置了淘宝镜像源，要去掉，重新设置成 npmjs 的源

先 npm get registry 查看npm源

![图片](images/WEBRESOURCE70201035fa78871727931dcff9ef7371截图.png)

设置 npm config set registry https://registry.npmjs.org/  直接指向 npm

![图片](images/WEBRESOURCE00c2d07003608ec9ac7e0b6c8aadbe9d截图.png)

这时候再 pnpm i 就可以正常装包了

![图片](images/WEBRESOURCE4910ca9478cfeec0f47bd10c2102c08b截图.png)

pnpm 升级 pnpm    pnpm add -g pnpm

![图片](images/WEBRESOURCEc886460f056bf4001c8cfe03b484d044截图.png)

![图片](images/WEBRESOURCE8e4cdbe55100ede63e3ac68505713ce4截图.png)

后面直接就可以用 pnpm（升到最新版本） 跑 不同的项目项目了 ，不用切换 node 版本（使用 14.x lst 版本）就可以

npm 和 yarn 不一样？

## 206、   if ... else if .. else  优化

（1）、单一条件优化

![图片](images/WEBRESOURCE5f7787c1cef997213dc6cb87d73a32d7截图.png)

（2）、复合条件优化

![图片](images/WEBRESOURCE0c960ffe544a31a6e4defc1f483048a1截图.png)

## 207、 break continue return

1)、 break 是直接跳出整个循环 （不再执行循环函数）

2)、 continue 是跳出本次循环（就是本次循环里continue 后的代码不再执行），然后进入下一个循环体

3)、 return 是 结束当前方法，主要用于方法的返回值（没有返回值可以返回空或者不返回）   return 强度 大于 break    ;    return 之后是整个函数（方法）都不会执行！！！ break是循环之后还有代码还会接着执行!!!!

![图片](images/WEBRESOURCEa2a639463e5d03f063e8fb155d3d583a截图.png)

## 208、pre 标签

![图片](images/WEBRESOURCE42333a34d5d578f87e8f6582c817122d截图.png)

![图片](images/WEBRESOURCE23404d45316db79e48774fe2bc9d18b5截图.png)

## 209、  vue-cli  和 vite  的比较

（1）、vue-cli 通过 NODE_ENV 指定环境变量

![图片](images/WEBRESOURCE002ced6086bbdbd71bc1b949bfddd557截图.png)

.env                                   开发环境 和 生产环境 都会加载的 配置文件

.env.development              只在 开发环境 加载的 配置文件

.env.production   只在 生产环境 加载的 配置文件

优先级：  同时存在的环境变量、  特定模式的环境文件（.env.development 和 .env.production）优先级高于.env的

修改了配置文件需要重启服务，文件内容变量才生效。

vite 通过 import.meta.env  中的 MODE 指定环境变量

![图片](images/WEBRESOURCE82d066790f1d822f49bdc9935505908b截图.png)

vite的 hmr 原理： hot module replacement（热模块替换）

![图片](images/WEBRESOURCE578b07b5497811adea7fb40af24ac14c截图.png)

![图片](images/WEBRESOURCEdc7d3be382c5166d735b668758e451e1截图.png)

![图片](images/WEBRESOURCE523298ba2a154f4415f8afb13c949060截图.png)

## 210、   简简单单，数组扁平化 （小 循环 + 递归）， 虽然可以直接 arr.flat()

![图片](images/WEBRESOURCE228afe8215cd19044c528ddfc71fb5de截图.png)

![图片](images/WEBRESOURCE3d5831145ebe5690ffe22be17e0da199截图.png)

![图片](images/WEBRESOURCE52173296fca7eb1dd9acc3e6386f67f4截图.png)

### 来个回溯：

递归是 reduce (  调用自己 return pre )

![图片](images/WEBRESOURCE91c77681bc44551dfd1f15572d297e45截图.png)

接着来 回溯 吧 ~  回到题目

## 211、  ( 一 )、 document.designMode = 'on'    开启网页设计模式 ，想改哪里 随便改 ！

![图片](images/WEBRESOURCE2a06e7cdd1df5600e3e94f936694de84截图.png)

( 二)、  document.body.contentEditable = true         网页内容可编辑

## 212、 css 属性 ： backface-visibility: hidden

![图片](images/WEBRESOURCE686a7ae55572ddae6abc2f70951eba2b截图.png)

![图片](images/WEBRESOURCEe8c5394f391283a18917779c3a1b5038截图.png)

设置背景图片（background-image）的透明度（直接opacity会是包括背景图片的整个DOM，而不是单纯只影响背景图片）

1、利用伪元素 ::after  div本身设置 position: relative; z-index: 0;  after 伪元素设置 绝对定位 和 上下左右都为0，content: ''， 然后再background-image: url(...)， z-index: -1; 就可以了。

![图片](images/WEBRESOURCEc984cf06883f123557922156b3fcfb80截图.png)

## 2、使用 cross-fade()  图像函数

### 语法：

<image-combination> = cross-fade( <image>, <image>, <percentage> )

这是一张空图片，只有一个点

data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==

![图片](images/WEBRESOURCEc12bc1719c5da79b9905842ff8c99554截图.png)

![图片](images/WEBRESOURCE78ba06b191c0a769a22fbda63447e65d截图.png)

### 效果：

![图片](images/WEBRESOURCE5c1bc530976112e4204d6e00fb054e62截图.png)

background-image 设置的div显示不正常时，要加上 background-size 属性。设置 cover

![图片](images/WEBRESOURCE18b2cead91d434dc3d33ddc0e2c8af81截图.png)

## 213、vue3 的 ref、reactive、toRef、toRefs

![图片](images/WEBRESOURCE976f6f61e586e5f915942fbf1391e4cf截图.png)

## 214、 vite 配置 https

![图片](images/WEBRESOURCE29da0497007e5a5b6369d82d53ccc059截图.png)

## 215、 vue 的几个 语法糖

### v-bind:   相当于 :

v-on:   相当于 @

v-slot:   相当于 #          v-slot 都作用于    <template>   标签中   （除了只有默认插槽的情况下可以写在组件标签上）

(slot-scope 2.6版之前，后面都是 v-slot)

v-slot:default   ===   #default   ===  什么都不写     默认插槽

v-slot:footer  ===  #footer    具名插槽     （定义是   <slot  name="footer">）

v-slot:footer="{ record }"    ===    #footer="{ record }"     作用域插槽       （定义是   <slot  name="footer"  :record="{ xxx: xxx }">）

## 216、

![图片](images/WEBRESOURCEb2d028d2df697bb775b26a28d4e1daa0截图.png)

## 217、 vue3 的子组件 接收  v-model  （可绑定 boolean / string / number / 数组 ）   和 vue2 的区别

v-model 绑定 元素（input、textarea、checkbox 单/多选框 加 value 属性、radio 加 name 属性、select） 和 组件

![图片](images/WEBRESOURCEd2768aff61480fb48114e87f651206cf截图.png)

![图片](images/WEBRESOURCE82afed458e2549c8d9eaf7b3ba3fee19截图.png)

直接套个label就行了，不用 for 和 id 的。

![图片](images/WEBRESOURCE0e928dccac1a5fb5448ca223e55e66d5截图.png)

![图片](images/WEBRESOURCEdaaed36f41bde9bf0a82ea5716e322ca截图.png)

![图片](images/WEBRESOURCE91695a67271992e287b3baa12e72cbfd截图.png)

有了 v-model，可以省略 name

