---
title: "2025 年度规划与目标"
description: "技术学习路径与开源项目计划"
date: "2025-06-05"
lang: "zh-CN"
category: "life"
tag: "planning"
tags: ["goals", "2025", "react", "ai", "learning"]
featured: true
---

新年开始，简单梳理一下今年想学的东西和要搞的项目。

## 技术学习

### React 生态

用 Vue 用了这么久，该学学 React 了：

- **Next.js** - 看起来挺火的，学学 SSR 这套
- **状态管理** - Zustand、Redux Toolkit 都试试
- **React Server Components** - 新特性得跟上

### AI 相关

这块变化太快，但确实有用：

- **LangChain** - 想搞个 RAG 系统
- **本地模型** - Ollama 这些试试，不想数据都上云
- **代码生成** - 看能不能做点工具提升效率

## 想做的项目

### 开源工具

有几个小工具想整理发布：

- **Vue 转 React** - 迁移代码时候用的脚本
- **微前端工具** - 解决一些实际痛点
- **AI 小工具** - 自动化一些重复工作

### 博客内容

除了技术记录，还想写：

- **微前端实战** - 项目中的真实经验
- **AI 工具使用** - 哪些好用，哪些坑

## 关于 AI 工具

用了一段时间 Cursor 和 Claude Code，确实提升不少效率。不过也发现：

- **简单重复的代码** - AI 写得很好
- **复杂业务逻辑** - 还是得自己想
- **代码质量** - 需要人工 review

感觉未来几年 AI 会越来越重要，但不是替代程序员，而是让我们专注于更有价值的事情。

总之，2025 年的目标就是多学多做，保持好奇心。
