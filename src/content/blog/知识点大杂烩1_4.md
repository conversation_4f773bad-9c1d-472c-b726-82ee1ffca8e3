218、前端页面做了校验？？不好意思，我 直接  接口文档（swagger）、Postman 改（插入）数据，直接改数据库， 不用过你前端校验，爱输什么输什么。。。

219、 git 改 https 为  git://

git config --global url."git://".insteadOf https:*//*

220、 undefined  和  null  区别 ？？？

(1)、默认情况下，两者使用没啥区别 

let a = null;   let b = undefined;  两者的作用基本一致

然后双等号 判断 也是 true  .     undefined == null      // true  

(2)、 null 表示 “没有对象”，即此处不应该有值，转数值为0       作为对象原型链的终点（  Object.getPrototypeOf(Object.prototype)  === null ）

         undefined 表示“缺少值“，即此处应该有一个值，但是未定义， 转数值为 NaN   函数无返回值为undefined

typeof null === 'object'   ?     怎么理解？？？

可以理解为是一个历史遗留的 Bug

js的最初版本是使用的32位系统，（js为了节约性能）使用低位存储变量的类型信息；判断数据类型时，是根据机器码的低位表示进行判断的，而 null 的机器码标识和 对象的机器码标识一样都是000，没有事先做过滤，导致误判了 null 为 'object'

js 中 变量没有类型，只有 值 才有 ！！！

221、 npm run serve / dev / xxx

![](images/WEBRESOURCEbbe46eff819551fe3d6e635ab4abbc2b截图.png)

![](images/WEBRESOURCE59baa2bd29892b33f57d1b4b50e9a6b0截图.png)

![](images/WEBRESOURCEb66b7ad37d1716a96ae45532642a421a截图.png)

222、   async-validator 的 rules集合中的每个对象的type默认是String类型的，做校验的时候可能会因为 number 和 string 来回切换 导致 校验不到 

223、动态类名 - 鼠标点击增加样式（文字颜色改变）

![](images/WEBRESOURCE74b917fba7ad8e8d945cd5cbedf239a1截图.png)

  

![](images/WEBRESOURCEb8a4712dc1dce2b816d77bb4f4938907截图.png)

![](images/WEBRESOURCE262818cf65e2c38f1c7685578584d311截图.png)

![](images/WEBRESOURCEe8dbac65cd32db4d1a91342444cbab59截图.png)

classList**.****contains**('active')   判断是否有该类名 做样式切换的时候

224、  git commit emoji:  

**git config core.ignorecase false****    配置 vscode 修改文件大小写时，当创建新的文件。不会导致 修改大小写时 远程仓库没更新到 。。。**

这是 git 内部就支持的功能，不用额外装什么插件显示对应的图标就可以看到对应的提交图标。就提交的时候在提交信息前面加上对应的 :xxx: 语法就可以了（可以装提交选择图标的插件）

:recycle: refactor: 重构 

:zap: perf: 优化 

:lipstick: style: 样式     

:memo: docs: 文档

:art: chore: 配置修改

:sparkles: feat: 新功能/特性

:bug: fix: 修改bug

:bookmark: carry: 不同仓库（项目）相同代码搬运  (自定义的)

![](images/WEBRESOURCE7f11c3c74d4910dbcb5b9f4c3e159e75截图.png)

225、 执行顺序  vue 模板 执行顺序： 

  render => template => el    el ---->    $refs.idName.$el  （整个Vue实例的 DOM 对象）

 

![](images/WEBRESOURCE476bb78d50c8b0cf22a244224f823096截图.png)

226、 websocket 与 后端的通信 

1）、与 http 比 ？  服务端可 主动 推送消息 到客户端  不用像登录扫二维码那样 不断的发送 http请求 轮询 接口，直到有返回数据才进行下一步的操作（页面跳转）

2）、大致使用？

⚪ 首先要装一个依赖包 reconnecting-websocket    ws掉线自动重连

使用： 

```javascript
const ws = new ReconnectingWebSocket('ws://....');
```

⚪ 然后初始化一个 ws

```javascript
import ReconnectingWebSocket from 'reconnecting-websocket'

initWebsocket(wsUrl, protocols) {
    const debug = process.env.NODE_ENV === 'development' // ws实例是否打印 debug 信息，默认是 false
    const options = {
        connectionTimeout: 1000, // 设置超时重连时间（没连上的时候一秒请求一次连接）
        maxRetries: 100, // 最多重连次数
        debug    
    }
    
    return new ReconnectingWebSocket(wsUrl, protocols, options)
}
```

⚪ 在主布局页面 进行 ws 的连接

![](images/WEBRESOURCE6f18e8280681c1f4783c8118eb5a0922截图.png)

详细代码为：

```javascript
import store from 'store'
import expirePlugin from 'store/plugins/expire'
const storage = store.addPlugin(expirePlugin)

websocketConnect() {
    const url = process.env.VUE_APP_WB_URL || null  // ws 的请求 api
    const token = storage.get(ACCESS_TOKEN)
    
    // ...详细代码看下面
}
```

首先会发送一个 websocket 的请求， 请求与服务端建立连接，状态码 101 表示 升级协议

initWebsocket()  方法

![](images/WEBRESOURCEe613d6cf3f8df094750a6a52fc3f57aa截图.png)

连接服务器的ws地址

![](images/WEBRESOURCE6dbc33990332493876a11fd68e1a7546截图.png)

227、  判断字符串是否是yyyy-mm-dd的日期格式

![](images/WEBRESOURCE6741ef4385df47ea6c995bacd2833694截图.png)

![](images/WEBRESOURCEac97fca6fe945f59cd249b5687df6dac截图.png)

targetLength

当前字符串需要填充到的目标长度。如果这个数值小于当前字符串的长度，则返回当前字符串本身。

padString 可选

填充字符串。如果字符串太长，使填充后的字符串长度超过了目标长度，则只保留最左侧的部分，其他部分会被截断。此参数的默认值为 " "（U+0020）。

// 判断字符串是否是yyyy-mm-dd的日期格式 包括日期正确性校验
const judgeDate = (() => {
  function format(d) {
    return [
      d.getFullYear(),
      `${(d.getMonth() + 1)}`.padStart(2, "0"),
      `${(d.getDate())}`.padStart(2, "0"),
    ].join("-");
  }
  return s => format(new Date(s)) === s;   // 返回值是一个函数 而且这里没有其他代码有调用这个箭头函数 正所谓 函数不调用就不执行   而这里用了立即执行函数 所以就执行了  最终的返回值是这个箭头函数的返回值！！！
})()

![](images/WEBRESOURCE780254a77bd85d6028d2216096a5b5b6截图.png)

IEFF 立即执行函数 

![](images/WEBRESOURCE1a8e318bfa466531b145cb4c4c454a4b截图.png)

命名空间 - 闭包环境 ？ （里面可以访问外面，外面不能访问里面，不会造成命名冲突，变量污染）

闭包： 内层的函数可以使用外层函数的所有变量，即使外层函数已经执行完毕

228、 扩展运算符解构对象，单独定义相同字段，不影响原对象的该字段。

![](images/WEBRESOURCEe268ad0b0a39097431edc30e1a29374c截图.png)

229、前端利用 jsencript.js 进行 RSA 加密

必须有私钥才能对 对应的公钥进行解密  （有公钥是不可能拿得到私钥的，私钥应该可以获取对应的公钥的）

对称： 通信双方公用同一密钥

非对称： 公钥和私钥，公钥加密、私钥解密

**RSA 加密： 非对称加密算法**

**使用： 需要一对 **密钥 ，**公钥和私钥 （一般公钥加密，私钥解密）,终端跑命令生成**

**公钥和密钥： 一组数字，其二进制长度为1024位 / 2048位**

使用库进行加解密

```javascript
import JSEncrypt from 'jsencrypt/bin/jsencrypt'
// 密钥对生成 http://web.chacuo.net/netrsakeypair
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBtToApvTzfpax/8OyYkVPKiCx0AFG5AXyZheW3CnL8LE0gQSKEpxghx8Odw306Ne1uOR5aNRMjaD4V9q5TDWtyiC28MgPALqOBZGVA3ZV7rx0xeuGu5IKJrtIQyICkRaKH4v+CRZnEAwGrBVxzjBPxUXlkC/TLpLFBSrtmrN99wIDAQAB'
const privateKey = ''
```

```javascript
// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对需要加密的数据进行加密
}
```

```javascript
// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey)
  return encryptor.decrypt(txt)
}
```

```javascript
对登录密码进行加密
```

![](images/WEBRESOURCE1af4e105f3b926acab7ae1594672d4e5截图.png)

window.btoa('字符串') 和 window.atob('bsae64编码字符串')   base64编解码 （js原生方法）  -- 对应有 js-base64 的库可使用

作用： 简单加密，bsae64编码兼容特殊字符，对应ASCII 字符串

使用：

![](images/WEBRESOURCE90baaad4a5ced48ec70c3d218915cff5截图.png)

230、 根据不同的终端设备显示不同的页面（非一套布局方案的响应式），是写两套的, PC + 移动端 mobile

1、首先，根据 navigator.userAgent   判断 当前终端是 PC还是移动端

2、然后在打包后的dist文件夹中的 index.html 中进行判断，引入不同的静态文件

vue打包过后生成dist文件,对比两套生成的dist文件，将共同的css，js写在一起，不同的通过

document.write 写到文档中，当然也可以用响应式的布局，但是响应式的布局加载慢，写法麻烦后期难以维护，小页面还好大的项目的不同点太多了，几乎跟写两套没什么区别

```javascript
function IsPC() {
  const userAgentInfo = navigator.userAgent;
  const Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"];
  let flag = true;
  for (let v = 0; v < Agents.length; v++) {
    if (userAgentInfo.indexOf(Agents[v]) > 0) {
      flag = false;
      break;
    }
  }
  return flag;
}

const flag = IsPC(); //true为PC端，false为手机端
if (flag) {
//pc端引入的link
  document.write(' <link href="css/app.fa43afb7.css" rel="preload" as="style"> <link href="js/app.ae6f97da.js" rel="preload" as="script"><link href="css/app.fa43afb7.css" rel="stylesheet">')
} else {
  document.write('<link href="css/app.4646bd09.css" rel="preload" as="style"> <link href="js/app.0b91c116.js" rel="preload" as="script">  <link href="css/app.4646bd09.css" rel="stylesheet">')
}
```

引入js ，在这里要注意一个点，当写的是双标签的时候在结束标签的前面要加个转译字符

'\' 不然无效

![](images/WEBRESOURCE7714c9d4876686122f8cbee924cef1b4截图.png)

231、  计算对象的层次 （递归实现）

/ 

232、lass 、 pass 、 sass

**lass**  底层应用 cpu / 网络 / 内存 等计算资源

**pass**  中间件  开发语言 和 开发环境 

**sass**  软件即服务 直接使用开发好的应用软件，只注重运营。

233、 git flow 工作流  

为了更好的管理项目，针对版本发布周期进行的一套代码管理，把控项目，降低项目上线的风险和提高可操控度。

有一个命令行工具 git-flow，是对 git 命令的封装，简化各分支的繁琐操作。自动将release合到master并打tag和dev，并在本地和远程都删除release，再切换到dev分支。

五个分支：

1、 master 主分支 运行 / 演示 系统（生产环境）的分支，是确保代码没有Bug时（测试通过的）才可以合并到此分支上

2、develop 开发分支 测试环境的分支，主要用于日常的需求开发提交代码的分支，代码未经测试员测试，可能存在bug，及不稳定   基于master创建develop

3、feature 新特性 包括小版本迭代（2.1.x），一个版本迭代可以开一个feature 分支，基于 develop 分支 创建 feature，新特性开发完成会合并到 develop 分支，但是不会跟 master 打交道

4、release 发布分支 开发周期时间到了/ 完成了指定的版本任务开发 ，从 develop 创建 release 分支， 用于代码发布工作。除了 相关的 bug fix 之外，不允许在该分支增加其他功能的代码。最终会合到master分支，顺便会打标签（tag），备注对应的版本号在master上

5、hotfix  专门用于打补丁，修改bug。 从master创建hotfix，待 bug 修改完成后，可以合到其他的分支上，主要是为了不影响其他分支的正常工作，同时又能协同进行 生产环境中（master分支）bug 的修复工作

234、 文字左右边对齐：  text-align: justify;  

 

235、 CORB 警告

CORB:   Cross-Origin Read Blocking  跨域读阻塞

为哈会有： 防止网络安全漏洞，出现的站点隔离（corb 实现策略之一）

什么时候会有：

![](images/WEBRESOURCE9071440978b7112e723cf98b887c304b截图.png)

![](images/WEBRESOURCE8f27637280a0d40a6e962ed282a6baea截图.png)

![](images/WEBRESOURCEfb11b090ece0ea87b157aa6a4849f5d9截图.png)

![](images/WEBRESOURCEe3992c34621591bb719c08865789a5f4截图.png)

![](images/WEBRESOURCEdc9f9df1cc616818f83d459be518d633截图.png)

236、 rem （font size of the root element） 和 自适应百分比      追求的是** ****比例 一致**

fontsize大小自适应  +  font-size大小固定

**1rem = 根元素（html）的字体大小** （大部分情况下  **1rem = 100px**）  （移动端下是 **1rem = 16px**）

设置  1rem = 100px，主要是：

 (1)、方便计算（设置 100px，相当于 100%， 把 1 分成 100 分，把100当成一个系数而已， 好计算啊）

 (2)、扩大基准值，适配部分限制最小字体的浏览器

       （chrome最小字体12px，华为个别型号浏览器最小字体8px,如果设置为1rem=10px,在12px最小浏览器中会被强制转换为12px，会导致基准不对比例计算出现问题）

 (3)、常见分辨率下（375px / 700px / 320px ....）的 1rem 都是设置为 100px

但是更多时候， html 的 font-size 是动态计算的呀！！！！ （为了不同设备上显示的比例一致）

 <HTML>元素 的 font-size 计算公式为：** ****( 用户设备宽度 / 设计稿标准宽度 ) * ****100**

设备像素比  device pixel ratio （DPR） = 物理像素  (设备像素) / 逻辑像素  (css像素)  

237、 简历  --  包装   --  获得面试机会

![](images/WEBRESOURCE320bd479920a8c015472584ee27b7682截图.png)

238、 window.open / location.href  会自动拼接上域名url，只需要拼上路径 '/path' 就行

**document.domain**   获取 域名（不带端口）

**location.host    **获取主机名（域名）（带端口号，如果有端口号）    /    **location.origin  **获取完整的URL（带协议 比如： http://）

![](images/WEBRESOURCE97962b53c3361fd43648a7fed32e9e45截图.png)

![](images/WEBRESOURCEaf6f93bc07ed3947d1599176e669e506截图.png)

（1）、因为会自动拼接，所以要加上 ' // '，不然会拼在自己的域名下。

![](images/WEBRESOURCE2d7c4c7a6dc7f598d6a72aabc2589bba截图.png)

（2）、还有一种方法就是使用 路由的 resolve 方法，进行跳转。就不用判断哪个环境了。

const openUrl = *this*.$router.resolve(name: 'xxx', query / params).href

window.open(openUrl, '_blank')

![](images/WEBRESOURCEa661fa1a4d9805941e7f888a86eed8c7截图.png)

其实直接 / （根路径）也是可以的，主要看你拼的参数能不能跳过去！

![](images/WEBRESOURCE0db89a3d6a1f8f91d52e111458fb1853截图.png)

# **239、**** 关于路由 一些比较悬的东西**

![](images/WEBRESOURCE46a10660b84c931a02f59f44e4c7c53c截图.png)

![](images/WEBRESOURCE944053cd602b9838912bb0ea37fa2598截图.png)

240、 技术的出现都是为了解决问题的、而不是单纯地了解一堆API的

掌握一门技术，要知道其局限性/历史 和 本质

学习也是单纯地为了解决问题的，不是为了去记忆一堆API的。

软件： 计算机数据 +  指令

大前端： 移动端 （unpapp） 和 web端 （网站 / 后台管理系统 / 手机H5）   小程序端  桌面端  服务器开发

241、  vuex-persistedstate  Vuex的持久化插件（将本来存在state的数据映射到本地存储中，既做到了刷新不丢失又能保证数据还是响应式的）

242、js 完全搞不懂系列： = =

{} + ''   结果是转数字 ----->  0

![](images/WEBRESOURCEf555ecdb142e4fbdae1397780af42967截图.png)

![](images/WEBRESOURCEe540272a99fff534e4bfd308a2400961截图.png)

![](images/WEBRESOURCE63253c642baa92757b59afde483247ca截图.png)

243、 循环 push ，如果把 字典 dict 写在 循环外面, 辉出现相同key 覆盖的问题, 解决方案是写在循环里面，确保每次都重新初始化，保证数据不给覆盖

![](images/WEBRESOURCEb84755fdc0720157aed33906e72a2472截图.png)

**分析原因：**

可以发现每次 for 循环添加到字典中，都会覆盖掉上次添加的数据，并且内存地址都是相同的，所以就会影响到列表中已经存入的字典。

因为字典的增加方式dict['aaa] = bbb,这种形式如果字典里有对应的key就会覆盖掉，没有key就会添加到字典里。

![](images/WEBRESOURCE5e9ef57c810249be545aaa294b55510f截图.png)

245、 关于表单校验的动态绑定校验值 动态绑定 prop 值

  关键点：    template循环该数组（绑定在form中的） + prop取值（   'prices.' + index + '.title'   组件识别这种写法 ）+ v-model 帮值  三者缺一不可

![](images/WEBRESOURCE0c312a4d72239e4d60f1d0594006a326截图.png)

主要就是  循环 绑定 model 中的 form 的数组 + 自定义prop + 自定义rules + v-if 判断  其他的rules那个字段对应的校验函数 一样的写法的

![](images/WEBRESOURCE90170e49deb2ae617b30c9438f82cfe1截图.png)

246、 vue路由跳转的方式

（1）、 router-link 标签 （a标签跳转会重新渲染即刷新页面， router-link跟 this.$router.push 那几个方法就不会，会守卫点击事件，让浏览器不再重新加载页面 只会更新不一样的地方。 所以使用了keep-alive的话就不要用a标签了，会重新刷新页面，使缓存失效的） 			           router-link 是 只会更新变化的部分从而减少DOM性能消耗

                           		  a标签的*rel*="noopener"属性 即 no opener 的意思，就是在同时设置了target="_blank"时打开的新页面中的window.opener值为null，安全起见，获取不到原有的window对象

![](images/WEBRESOURCEba5335d3c2f70189e124c059d7f4c6d6截图.png)

（2）、this.$router.push / replace / go /back /forward / 方法

  router 的两种传参方式 ： 

query: { id }   问号拼接

![](images/WEBRESOURCE3fd871a291a39290fdecda4767070e75截图.png)

params: { id }   斜杠拼接

![](images/WEBRESOURCE80d8c3d8af41cef8383e158adc7b9f14截图.png)

this.$router.push('/biz/production/edit' + id)  params  拼在  url 上 （刷新页面有）

 等价于  

this.$router.push({                 params 在 body 上 （刷新页面无）  

  name: 'productionEdit',

  params: { id }

})

247、 keep-alive 的 使用： 

跳转的时候如果都是用的vue-router提供的方法（router-view或者$router.push等），那么这个keep-alive会自动帮你缓存，就你请求过的再次请求就不会再请求接口了，除非你手动再刷新页面。

浏览器的前进后退功能也是一样的不会刷新页面的，

那如果你是点击切换请求接口的话，那keep-alive就做不了了。每次都重新请求接口，就不会帮你缓存了 。

248、算法   计算/解决问题的步骤。 

线性结构（数据对象一对一）： 数组、链表、栈、队列 

非线性结构：  二维/多维数组、 广义表、树结构、图结构

249、控制台获取element元素的dom对象

![](images/WEBRESOURCEdbad49b052adbb0bf15f3ad8b5030a74截图.png)

dom元素和组件实例的区别：

![](images/WEBRESOURCE46c67d31bc35985b89e219a2509faf1c截图.png)

dom元素可以当做ref得到的实例对象中的$el属性

![](images/WEBRESOURCE8c1de339aa1fcfa01a471ddd76cf3509截图.png)

控制台获取element元素的dom实例

![](images/WEBRESOURCEde7665e7303b535e82f5946b11b92808截图.png)

vue组件可以分3种

根组件： 最顶层 id="#app"

子组件： 根组件下面的组件（可以多层嵌套）

琉璃组件： 挂载的 全局 $xxx 属性

一个组件就是一个实例

![](images/WEBRESOURCEb3aac941b74f893eb1382c9f8dddcd9f截图.png)

250、  项目难点： 

（1）、公用组件的抽取（抽象功能，进行组件的封装    插槽 / prors / emit  ），避免很多重复冗余的代码，同时也提高了开发效率。

（2）、框架的自定义配置 （递归显示菜单 menus 模块）

（3）、系统管理模块

（会员中心）用户（账号）管理   ---    （管理中心/管理后台）人员（管理员） 管理

角色（权限）管理

资源（菜单）管理

（4）超时退出重新登录回到原先操作的页面  

（   点击确认退出按钮的时候 disptch 到 logout 接口，然后成功后的.then中  this.$router.push('/login?redirect_uri=' +  this.$route.fullPath) 保存当前操作页面路由，

       然后在登录页 this.$route.query 获取到 redirect_uri 直接 this.$router.push 过去就可以了，然后没有  redirect_uri 就默认跳到主页 ）

1、资源显示当前账号下拥有的所有资源（菜单）列表，超级管理员默认拥有显示系统全部菜单的权限（可以操作菜单，排序，显示隐藏，更换菜单图标/菜单排序，是否缓存，组件路径等...）

![](images/WEBRESOURCE8bcda09c6072bf82e9585cb6eac99930截图.png)

![](images/WEBRESOURCE6f37c51502e35eff37514bd4540fb3cb截图.png)

2、角色管理可以为系统创建角色（给用户分配角色），分配角色菜单

![](images/WEBRESOURCE3ba63e393f7c04ebc98e1f5428680158截图.png)

3、账号管理是登录系统用的账号名，可以新增 / 修改密码，分配角色，是否启用该账号，删除账号 等

![](images/WEBRESOURCEbc694c8f138d9607c8abe3e08222e3fa截图.png)

251、 input 的 type 为 number 时，设置的 maxlength 的写法：

<input type="number" oninput="if(value.length>5)value=value.slice(0,5)"  />

252、 ssh （secure shell）安全外壳  ，。 连接远程服务器用的一种 网络安全协议。 默认端口号是22 

253、 eslint   代码 **质量 / 风格**  检测工具      linting    检测？  （eslint 代码规范？ ts 类型规范？） （更强调 逻辑/命名 ？）              语法/变量/错误等		JavaScript代码检查工具

   prettier  代码风格格式化工具  											           （更强调 缩进/格式化代码 ？）   缩进/换行/分号/引号	代码格式化工具

lint-staged 限制代码提交规范（提交 （git add） 的时候自动跑 lint 命令）

![](images/WEBRESOURCEb164b1a67101b821d0a0c4f5a4e9b8ed截图.png)

![](images/WEBRESOURCE35f3a8affcd831b6423561a4fb24129a截图.png)

这样配置就可以了

254、 QQ浏览器首页的**视差滚动效果**：   background-attachment: fixed   和  js控制 background-position-y 产生位移

255、  判断对象是否为空 ( 1. Object.keys().length   2. JSON.stringify({}) === '{}'   3. for ... in 判断   4. Object.getOwnPropertyNames({}).length === 0 )

![](images/WEBRESOURCEb6d8b5ac10ad55cbba83f0e2350ee461截图.png)

![](images/WEBRESOURCEc4b045ac106b7863b886f6301b280733截图.png)

![](images/WEBRESOURCE64e6b43b9fcca1ba29e89129f516a119截图.png)

256、请求接口的方法：** form表单（action属性**）**提交**（会引起页面跳转） 、  XMLHttpRequest、 Ajax、Axios   

   

257、代码要每天都提交一次！不要按功能完成再提交，确保 服务器能备份到最新的代码，确保本地不会因为硬盘坏了而丢失代码（即使是万分之一的概率）。

提交的时候只要确保基本的编译不会出错就可以了，只是提交到你的 feature 分支，不会影响到 master 分支的完整性的。 到时候一个功能开发完成了再通过测试后

再合并到主分支就可以了。

参考 **devOps** 的操作流程。

CI / CD 持续集成 / 持续部署（交付） continuous integration

  continuous deployment（delivery）

258、命名： 数组： menu**s**   menu**List   **Nodes records

对象： **data**  menu**Info **menu**Dict **  

Map: menu**Map**

Set: menu**Set**

259、用  img标签的 **onerror（@error）事件  **解决图片的src地址访问不到报错（或者图片裂开）的问题

![](images/WEBRESOURCE97d00e128d245b745d0a906837e9cefa截图.png)

原生：

![](images/WEBRESOURCEa59f38776a6183200d9317b907544a98截图.png)

![](images/WEBRESOURCE66f11cc5f12a433ebfdbc409d23c684d截图.png)

VUE:

![](images/WEBRESOURCEa7aab5083069e404223b697c244715da截图.png)

![](images/WEBRESOURCE01eff6f749c9ce9c5e25b85ad93c1021截图.png)

260、 可拖拽的视口

![](images/WEBRESOURCE141415efd3f899f32cbdbb2d32045d78截图.png)

 

cursor: col-resize;

 

261、 获取 **外部样式** 的 方法

window.getComputedStyle( ele )  获取该元素的所有外部样式对象 （有width属性 ......）	  // 返回值是 整数

ele.getBoundingClientRect( ).top / bottom / left / right   返回一个矩形对象，包含那4个属性    // 返回值是 精确 的 带小数点的

ele.offsetWidth /  offsetHeight / offsetLeft / offsetTop        						           // 返回的是 整数

![](images/WEBRESOURCEdf6e491ebb185297a5f564eda9e8a3d5截图.png)

262、 package.json 文件中的 dependencies（项目的依赖）  和 devDependencies（开发所需要的模块）  和 peerDependencies 的区别：

（1）、如果没有发布 npm 包， 那么依赖放在哪里没有区别；

（2）、为了规范，如果是一直使用的包（项目中会一直用，如 ant-design-vue、day.js等），放到 dependencies 中; （这里的依赖是一定会被下载的）

  如果是开发时需要用到，上线后（线上环境）不会用到的，如 webpack、eslint、prettier、各种-loader、stylelint等...，放到 devDependencies 中

（3）、peerDependencies    解决核心库被下载多次，统一核心库的版本问题   （项目依赖了 vuex 和 vant 这两个子库，而这两个依赖又都同时依赖了 vue 这个框架。在字库中分别声明 peerDependencies 防止重复安装 vue）

![](images/WEBRESOURCEc211f1a9c8579be708b19fd63cfe8238截图.png)

![](images/WEBRESOURCE5e6d81d5ca397512a7cdf807720497a2截图.png)

（4）、peerDependenciesMeta   对 peerDependencies 的修饰， 增加一些可选项的配置 。

263、 关于 css 字体 的一些学问  **font-weight**  属性在不同操作系统（常见win和mac）上的显示效果的不同

（0）、关于 win 和 mac 的默认字体 （不设置font-family属性，会自动读取操作系统的默认字体来显示的）

![](images/WEBRESOURCE166d88b8229ee4226d547dc48abb063b截图.png)

（1）、win上 ：600为分界线。600往前一个效果，600往后一个效果

      mac上：每一个值显示的效果都不一样

![](images/WEBRESOURCE5bdf8a6a725de89b82a4dcf80dbf49bc截图.png)

（2）、因为操作系统和浏览器的原因，字体在不同操作系统设备上显示的粗细会有所不同。这是 font-weight 这个属性存在的兼容性问题

![](images/WEBRESOURCE949b061644d5ffe6968a8e87c5ca6a81截图.png)

（3）、使用 @font-face 引入自定义字体，font-family 使用该字体名称   （字体后缀名 .ttf  .otf  .fnt ）

![](images/WEBRESOURCE5d9c4e9fbc8ebc9e94738d5b4814f81f截图.png)

![](images/WEBRESOURCE3d513376b6dc1586dbf88cefc765c5d5截图.png)

![](images/WEBRESOURCEde5d7be89a71a3021be0d7c803882a3c截图.png)

264、vue 中 多个组件使用  window.onresize，只有一个生效，导致其他的给覆盖。

![](images/WEBRESOURCEe8b4bb46a90e055ff3fda485524efd02截图.png)

![](images/WEBRESOURCEee606fc35d578068f19537e5e8665ed8截图.png)

265、js引擎（spiderMonkey、V8 ...）  （js解析器） 和  js编译器 （babel （es6 转 es5，转化 jsx 语法，兼容旧版浏览器，ts语法）、 tsc （typescript compiler）、 swc （speedy web compiler））

![](images/WEBRESOURCEe53b4fb6f87071e8bde0ab38e1c6cd1d截图.png)

266、commonJS （服务端 node）  和  ES Module 的区别：

(1)、require 和 import 的

(2)、CommonJS 的思路就是将函数当作模块

267、 JS的对象遍历是无需的（因为对象本身就没有存储顺序）。但是数组是有的，有index索引，记录每个元素的顺序。
 


![](images/WEBRESOURCE77d7c78acd977400a19abc6c57300756截图.png)

![](images/WEBRESOURCEc0f698cf324163b7e8cc47b75a007dc8截图.png)

![](images/WEBRESOURCEc2e1bb8bf07d8923b6f65ea8706c07e3截图.png)

![](images/WEBRESOURCEdf5c03bc370ca631f23a9c0683ccf89c截图.png)

 

268、JS（V8）垃圾回收机制：   GC  ----->   **Garbage  Collecton** **垃圾回收**    垃圾收集器     （自动回收机制） （C / C++ 就没有）

![](images/WEBRESOURCE72fdad86372ecfad7f69b2902b44dce4截图.png)

标识无用变量方法（GC 跟踪每个变量是否有用，无用变量打上标记，回收其占用内存）：    标记清除 （V8） 和   引用计数

标记清除会导致内存空间分配不连续（内存碎片）、分配速度慢

![](images/WEBRESOURCEd87407baac9d352a1e9398c26bb22079截图.png)

标记整理：

![](images/WEBRESOURCE5b03b0228253c5fd3069a8e14736ec6c截图.png)

引用计数（      跟踪记录每个值被引用的次数。

![](images/WEBRESOURCE3347afb78f2ff8cc1650526b72db91fc截图.png)

）

![](images/WEBRESOURCE0e84ac809a5bb9a28c5f92aa8bc95d0a截图.png)

上面 即 循环引用的 例子

![](images/WEBRESOURCE007d5cfe1006ba29f9b0d58b8be5693f截图.png)

回收

269、vue 动态绑定属性名

![](images/WEBRESOURCEf1e0af80a815303beb08ccb7f564a8e8截图.png)

**270、**    vue 中  v-for   支持的类型     （比原生的 for 屌很多的） in  / of  这两个都可以，作用完全一样（跟原生是不一样的）

1、遍历 数组（字符串）

2、遍历对象   value - key - index

             （键值对索引）

![](images/WEBRESOURCE30586ded7c557f02528c828312ed0c88截图.png)

![](images/WEBRESOURCE90332e754d821609c3efdb52b63e2522截图.png)

3、遍历数字

4、遍历 itarable（可迭代）对象

**key  属性 ** ）

### **Vue3.x 新增 自动生成唯一的 key 值**

![](images/WEBRESOURCE8f9dbff715267a723372b9332c2511bc截图.png)

### **Vue2.x 必须写key **

![](images/WEBRESOURCE4edf34a6a02da628d225f71bf337402e截图.png)

VNode  虚拟节点  

template中的每一个标签元素 vue 都会转化成 一个个的 VNode 

template -> VNode -> 真实DOM

![](images/WEBRESOURCE84da67a6f392a555ec3d5946e9174c39截图.png)

虚拟DOM ？  可以干嘛？

（1）、做 diff 算法 （绑定 v-for 的 key）

（2）、跨平台。vue 可以写 PC Web / 移动端 / H5 / 小程序 /  甚至 桌面端 / 安卓 / IOS ......   靠的是 VDOM 的 转换

![](images/WEBRESOURCE2abf9f03ce3c628937fc78f6c7d38d34截图.png)

271、　position: fixed;  一般都是相对于 屏幕视窗 （浏览器窗口） 来进行 定位 。（屏幕尺寸大小） 

  所以 设置 width 要动态计算 calc（）函数  除非 

![](images/WEBRESOURCE6d33b86a379c78b7bee94a8b522df13a截图.png)

272、  Vue 组件库  相关

（1）、直接用

```javascript
main.js 文件
// 组件库
import Element from 'element-ui'
 
Vue.use(Element) // 使用组件库的所有组件（全部加载，不管用没用到）
```

（2）、lazy-use  ** 按需引入**

```javascript
main.js 文件
// 组件库
import './lazy_use' // use lazy load components

lazy_use.js 文件
import Vue from 'vue'
import { Input, List, Dropdown, Tooltip ... } from 'ant-design-vue' // 按需引入

Vue.use(Input)
Vue.use(List)
Vue.use(Dropdown)
Vue.use(Tooltip)
...
```

如果仅仅是完成上面的操作，还是不能实现真正的按需加载的。

需要下载 babel-plugin-component 插件 并且 在 babel.config.js 文件中进行对应的插件配置（按需引入的组件库和按需引入的样式）

![](images/WEBRESOURCE6929495e782ba924ace6263abc55d0ad截图.png)

![](images/WEBRESOURCE8d62fc8dd95bf066fc1843674d8b9fbd截图.png)

以上，就可以做到按需引入了。

重写组件库样式： 

1、建element-ui.scss文件在style文件夹中，在index.scss中@import引入，然后在 App.vue 文件的 style 中 @import 引入

![](images/WEBRESOURCEcea01ab6b8562cd26e31893ce93c89fd截图.png)

![](images/WEBRESOURCEe70fd8306645b8cab322369a0c8bda61截图.png)

![](images/WEBRESOURCEa198f7db22bd2797d585e1c118802892截图.png)

2、建global.less文件重写 antdv 样式，在 main.js 中直接 import 该文件

![](images/WEBRESOURCE2a0fddc163be903ef2329434656dd1a4截图.png)

这样就可以 了。

**element-ui 的 layout 布局 中的 响应式布局**

```xml
<el-col :lg="{span:'12-25'}"></el-col>
```

```xml
.el-col-lg-12-25 {
    width: 48%;
}
```

:lg="{span:'12-25'}"  和  .el-col-lg-12-25    =======>         即 el-col-lg-12-25 设置的 width 就是 :lg="{span:'12-25'}" 对应的宽度     （12-25  就是从第12列到第25列  的意思  ）

![](images/WEBRESOURCE457599bab53401971afe485161862ec4截图.png)

273、 property 和 attribute  的 区别 （类似  $attr 和 $props）  

1、 **property****（如 **style,className,childNodes,classList ...**）**

**（1）、** 指的是操作 DOM 的属性 像获取到DOM后设置 xxx.style.color、xxx.className、xxx.childNodes、xxx.firstChild  .......

（2）、值只能是字符串（操作JS）

（3）、长属性名是驼峰写法

![](images/WEBRESOURCE4b9332da1104d643130fc0cc1cdb9056截图.png)

2、 **attribute****（如 **id,class,src,title,alt ...**）**

**（1）、** 指的是操作 HTML 的属性  像设置和获取DOM元素的属性  xxx.getAttribute('class')、xxx.setAttribute('title', 'cusTitle')  

（2）、值可以是 数组/ 对象等（HTML）

（3）、属性名

![](images/WEBRESOURCEad7de01eca9e34de97c7af868fc1797b截图.png)

![](images/WEBRESOURCEab733e26e74cecc0d7415c9fac0be508截图.png)

e.g.

![](images/WEBRESOURCE3cbfc8c2dc63d191cf78508d21f5ff1b截图.png)

![](images/WEBRESOURCE9bf20989904ceb30a54b223b2d14ab1a截图.png)

![](images/WEBRESOURCE4089be42d661990f2f7270c3cb7c4e28截图.png)

$listeners （vue3 deprecated）

![](images/WEBRESOURCE8d0e3c1cbd82619cc3d84f01dceb5e49截图.png)

![](images/WEBRESOURCEecef1504a7dd8197feb79c3431d706af截图.png)

v-bind="$attrs"  v-on="$listeners"     $options ===> options API 整个 获取自定义属性

事件监听的具体使用：

父组件：

![](images/WEBRESOURCEf95369162aa977929a8a4ae522e81fdc截图.png)

子组件：

![](images/WEBRESOURCEa46fb5efdcfbc588aa054694fb9242ec截图.png)

这里的v-on="$listeners" 就相当于 继承了 父组件中的除了 @run 之外的所有事件监听了（也就是红框框出来的那些事件）

也相当于：

![](images/WEBRESOURCEc4aff3e32d445fb22c7377a4d0a5fc20截图.png)

替代了    手动写$emit 事件  。

所以，这个 @run 在 父组件定义就行了。 在子组件这里再次定义的话就相当于 你执行了 两次 run 了。

![](images/WEBRESOURCE37aa2edf540133907dbbf7709de010ed截图.png)

274、Vue 的 install 

![](images/WEBRESOURCE08333719a0969c1717e45d9e7591cc8f截图.png)

275、flex （一维 / 单行）           grid （ 二(多)维 / 多行 ）

**让最后一行列表左对齐**

(1)、grid

```javascript
 display: grid;
 justify-content: space-around;
 grid-template-columns: repeat(auto-fill, 30%);
```

![](images/WEBRESOURCE6ae5f93f4e055bc325a2fb51f2dfb965截图.png)

```javascript
// 子元素
width: 30%;
height: 100px;
background: pink;
margin: 12px;
```

(1)、flex

1. 粗暴点，直接给最后一个 margin-right

2. 在后面套两个 <div></div><div></div>   (空元素占位)

![](images/WEBRESOURCE2b74f7b18b1d5a2806c6e4ae00772e91截图.png)

276、JSON Schema  

JSON : [JavaScript Object Notation](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON#javascript_object_notation)  js对象（万物皆对象）注释

**JSON** 是一种语法，用来**序列化**对象、数组、数值、字符串、布尔值和 [null](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Operators/null) 。key 和 value 都是 字符串

**JSON Schema 是一套 JSON规范，用于校验JSON的工具。**(格式有点类似于AST？)    就是一份JSON文件的说明文档，叼一点，可以生成form表单（插件支持）和做数据（字段）校验

包含了「表单数据描述」和 「表单校验」功能。

基本格式：

![](images/WEBRESOURCE2449656b67422bce6c6a1585ea4d5c58截图.png)

 json格式是不支持注释的，所以可以用 json schema 来定义json，在json文件里加个description字段就可以了。title是定义标题的。

field       widget 组件

语法：  定义了一系列的规则（关键字）     ajv:  JSON Schema 的校验器

组成：

#### **1、schema：**** ****用于描述表单数据的 JSON Schema**

![](images/WEBRESOURCE365b1420adc78d6e4722dd26b90807a4截图.png)

![](images/WEBRESOURCE3b3cb23f3801800fb6b153f434c51d46截图.png)

#### **2、ui-schema：  （ui 也可直接配置在 schema 中）****  用于配置表单展示样式**

![](images/WEBRESOURCE8029c07147f584f210d621bca9161386截图.png)

**3、error-schema ：****用于配置表单校验错误文案信息 **

.......

关键字：

1、$schema 关键字：声明了针对哪个版本的JSON Schema标准编写模式。

2、$id 属性：为JSON Schema实例声明一个唯一标识符；声明了一个解析$ref 的URI时的基础URI

3、$ref 属性：引用同一个JSON Schema文件或其他JSON Schema文件中的JSON Schema实例片段。这个属性的值通常是一个相对或者绝对URI，# 符号后面的部分是JSON指针

4、dependencies 关键字：属性依赖：如果JSON实例拥有对应的属性名name，则也必须有name对应的propertyset数组内的所有属性名

5、definitions 关键字：这个关键字可以在JSON Schema实例中定义一个可以在这个JSON Schema实例中被引用的JSON子模式。

277、同一个盒子同时使用 position 和 flex 会产生 冲突 （flex居中 和 子元素 position: absolute 之间产生的冲突）， 给盒子外面再套一层 div，相对 div 定位

278、声明式编程  （**函数式编程** 是 其中一种） 与之对应的是 命令式编程

![](images/WEBRESOURCE39b12c6c985158acebe7ff8fb6215dae截图.png)

279、React

（1）、开发 web 页面

（2）、React Native 开发 移动端跨平台

（3）、React VR 开发 虚拟现实web应用

依赖包： react  /  react-dom（ 渲染到不同平台的依赖，比如 react web 和 react native ） / babel

vue只要数据更新,会自动进行render,视图上显示的是最新的数据。

而react默认不会自动执行render (可以是 html元素 或 组件--------类组件和函数式组件)的，要在数据更行后手动执行一下render拿到最新数据

script标签中加 type="text/babel" 为了能让 babel 解析 jsx 代码

函数的返回值默认是 undefined （没写 return 的话）

es6的 class 默认是 绑定的严格模式下的，所以 class 里的 this 如果原本是指向 window 的，会变成 undefined  （使用 babel 转换的代码也是，默认都是开启严格模式）

280、禁止移动端的触摸事件（touch event），可以强制不显示浏览器的滚动条（比如 safari 会自动显示滚动条）

** touch-event: none;**

281、VUE_BASE_API 在   .env等 配置文件  中 ：

（1）、完整的 url ：  那就是 **base_url **+ api文件 中的 ** request_url **

![](images/WEBRESOURCE9be4cddb2b4ef5e1343b4d9cbd2fbb2e截图.png)

本地（开发）环境  和 测试环境 都是 这个的话，那访问的 api 就是 

![](images/WEBRESOURCE856496b4b0bf572220ba7edbbeb3c3f8截图.png)

生产环境 的 api

![](images/WEBRESOURCEe807ef62eaf6bb7e3a5574ae94a44fb8截图.png)

![](images/WEBRESOURCE4ccfdc3ff36a3c574969f92a61cc26cc截图.png)

（2）、单纯写 /api     这个是在 nginx.config 配置文件中进行 配置的。  （nginx是部署在服务器上面的，不是在后端代码中的。nginx 服务 配置项目）

![](images/WEBRESOURCEd36db036d6fe42358d5baf3bbcb11f69截图.png)

1、如果是在 开发环境 中，那么访问的 baseUrl 就是 http://172.21.44.15:8080 + requestUrl 就是 /api/user/search 

（如果没在vue.config.js文件中的devServer的proxy中设置 ‘/api’ 代理的话，那就会报 404 not found 错误。要改VUE_BASE_API的）

2、如果是在 测试环境 中，那么访问的 baseUrl 就是 http://api-test.xd0760.com + requestUrl 就是 /api/user/search

3、如果是在 生产（正式）环境 中，那么访问的 baseUrl 就是 https://api.xd0760.com + requestUrl 就是 /api/user/search

同理，VUE_BASE_API_UPLOAD 也一样，设置 不同环境下的服务器存储文件（图片、视频等）的地址，如果是配置的 /uploads 的话，要注意对应环境，如果是测试环境 就会报错了

![](images/WEBRESOURCE31ac36b2ba413cadeea833faf959e45c截图.png)

282、随机生成颜色

![](images/WEBRESOURCEdb71acf1ec8aa03a16009ad5c9593054截图.png)

const createRandomColor = () => `#${Math.random().toString(16).slice(-6)}`

283、原生方法 监听 本地存储 （localStorage）的变化：

window.addEventListener('storage', (event) => {

  console.log('storage changed', event)

})

![](images/WEBRESOURCE11c5f030d960d1be9d8df416162fe239截图.png)

![](images/WEBRESOURCEff1435ff838c3b3669093bc002d0e340截图.png)

var orignalSetItem = localStorage.setItem


localStorage.setItem = function(key,newValue) {
      var setItemEvent = new Event("setItemEvent")
      setItemEvent.newValue = newValue
      window.dispatchEvent(setItemEvent)
      orignalSetItem.apply(this,arguments)
}


window.addEventListener("setItemEvent", function (e) {
    alert(e.newValue)
}) 
localStorage.setItem("name","wang")

284、  修改  node_modules   上的代码：    用 patch-package  （依赖 git diff 实现的  patches 文件夹）   呀

注意: 要改动的包在 package.json 中必须声明确定的版本，不能有~或者^的前缀。

（1）、安装patch-package

```javascript
npm i patch-package --save-dev
```

（2）、修改完依赖后，运行patch-package创建patch文件		npx  执行 node_modules 文件夹 下面的 .bin 可执行文件

```javascript
npx patch-package 依赖名称
例子： npx patch-package element-ui
```

（3）、修改package.json的内容，在scripts中加入"postinstall": "patch-package"，这个是为了npm install的时候自动为依赖包打上我们改过的依赖

```javascript
 "scripts": {
　　...
　　"**postinstall**": "patch-package"
　　}
```

285、 vue 的 各版本 对比：

286、编程英语：

pseudo-element： 伪元素      

![](images/WEBRESOURCE6eb0ff03168d846bc57353e882583e95截图.png)

locale： 特定语言环境的（当地的） 'zh-CN'  'en-US'  （'ar-EG' 阿拉伯语言） to**Locale**String()  to**Locale**LowerCase()

SDK： **software development kit  **软件开发工具包

accessibility： 无障碍 （a11y） ----视  / 听 / 行动 / 理解 

patch： 打补丁

bundler： 打包（工具）（webpack / browserify）

chunk： 块

bundle： 束

vendor： 供应商（第三方库）

我们直接写出来的是 module，webpack 处理时是 chunk，最后生成浏览器可以直接运行的 bundle。（不是多少个 chunk 就会对应生成多少个 bundle ， （虽然大部分情况下会是一一对应） ）

invoke： 调用 （函数调用等） 

revoke： 撤销

implicit： 隐式的...

explicit： 显式的...

hoisting： 变量提升 (var关键字声明才会有)

indicate： 表明，显示

anonymous： 匿名的...

nested： 嵌套的..

polygon： 多边形

immutable： 不可变的  （immer 一直如此  immer.js）

coupon： 优惠券

trie：字典树

traverse： 遍历

linked list： 链表

expanse ： 花费 （消耗  expansive 贵的）

manifest： 清单文件（）

**fallback： **兜底

estimate：  估计放

diagnostics： 诊断

concurrent： 并发

shims：   垫片      shims.vue.ts

Polyfill：  填充器

**关于符号：（prettie）**

semi： 末尾加不加分号 （“false”  "true"）一行的结束

semicolon： 分号

semi-spacing： 强制分号间隔；分号前后是否空格

brackets： 括号

**comma**： 逗号

**trailingComma**：多行输入尾部加不加逗号

![](images/WEBRESOURCE21742d03a98500f6921a6fd1e6574696截图.png)

单引号： singleQoute

双引号： doubleQoute

wrap： 换行 （nowrap --- 一行显示  white-space   只有一行文字的时候）   wrapper： 包装器

287、浏览器使用 export / import  语法：  异步方法

默认是在 webpack/cli、 babel 等 脚手架/编译 工具 中有集成 / 做了代码转换

![](images/WEBRESOURCEa4edeb5736358d3f1db814ebf4c62c7a截图.png)

（1）、使用 type="module" （静态引入）

![](images/WEBRESOURCE147ea7064365ed4fe8f9d269dcb13992截图.png)

（2）、动态引入

![](images/WEBRESOURCEb23eda0025ac8fe494ef84632e355a2c截图.png)

（3）、解构（async / await）

![](images/WEBRESOURCEebd7772d89bc24bc6017903786b65d61截图.png)

288、前端模块化  （js 模块化规范） ----   UMD / CMD /  AMD  /  ES Module （esm   ） /  CommomJS（cjs）

**一、 CommonJS  **

用 module.exports  导出模块,  用 require 加载模块

CSS 模块化（防止 文件命名/样式 冲突）：

命名规范（BEM[ block__element--modifier ]、OOCSS [ 原子类 类似 tailwind 思想 ]、SMACSS、ITCSS...）、

CSS Modules（主要 react 在用）、 

![](images/WEBRESOURCE9a9faffe6b26363563e7749d9bf83c10截图.png)

为 <style> 区块添加 module 属性即可开启 CSS Modules。

![](images/WEBRESOURCE75286212f7ca1048ba354f7796dbcbaf截图.png)

![](images/WEBRESOURCE06a3602af065ca00895e4b085e19edb8截图.png)

CSS-in-JS （styled-components    react在用，相当于 vue 的 style scoped）  【 html-in-js： JSX 】    "all in js"

![](images/WEBRESOURCEf53af3c4a272c12a1541c915af980e7b截图.png)

[emotion](https://link.juejin.cn/?target=https%3A%2F%2Femotion.sh%2Fdocs%2Fintroduction) 排名第二的维护者 Sam 所在公司弃用了 css-in-js 方案，使用css-modules的解决方案，**主要是出于性能考虑**。还有增加了包体积

289、** ****Div****+****contenteditable**    实现   **可插入自定义内容（标签）****  **的输入框   （简单的富文本输入框）

290、jenkins 是 java开发，用于自动化构建（打包）和部署项目  ---  配置命令，自动跑

          docker 是 go开发，是个虚拟机，是容器

291、第三方统一登录SSO

带参数跳转回调会自己项目中。

292、 chrome devtools   [chrome devtools 开发者文档](https://developer.chrome.com/docs/devtools/overview/)  实用小技巧： 

// **$_**  (获取控制台上一次的结果)

// **$ 1-4**  (获取 $0 的 previous 元素)

// $(' 类名/id/属性名 ') ** $('.main')**   ===  document.querySelector('.main')    $ 就是 document.querySelector() 的别名（语法糖）

// :has()  :contains()

// table / dir /   **clear()**   代替 console.table / dir     **console.clear() **

**//  **监听事件 ？  像 vue 一样 在方法中打印 当前元素的DOM对象事件 (e) / ($event) ?  使用 **monitorEvents** 这个 api  啊

![](images/WEBRESOURCEf04b1efa45f243c72185ceb9255f1983截图.png)

![](images/WEBRESOURCEb83c9e47767d63073683ffc4ffb83636截图.png)

// **monitor** 相当于 vue 中的 watch 了 ，对函数做监听 ？？  取消就 unmonitor 了	

// **:has()**  选择器  和  **:contains() ** 选择器

1. 选中并删除所有带“关键字”的评论
$('.reply-wrap**:contains(关键字)**').remove()


2. 选中并删除所有用户等级在三级以上的评论
$('.reply-wrap**:has(.l3)**,.reply-wrap**:has(.l4)**,.replay-wrap**:has(.l5)**,reply-wrap**:has(.l6)**').remove()

293、  hash 和 history   区别  （浏览器的  /#  重定向 问题）

（1）、如果是 带 #/ 的话，要编码一下的， 用 encodeURIComponent 转一下，再传过去，不然会报错。（可能接口做了处理，特殊字符（#、等）传过去可能不被正常接收 / 浏览器处理？）

![](images/WEBRESOURCEc90e973f616f2ad69f93b466dc71882e截图.png)

history 可以配置 nginx 文件

![](images/WEBRESOURCEee2be3f34eb5d09bf7dec198f878686f截图.png)

（2）、加相对路径：

![](images/WEBRESOURCEb4feb3382db50b4ec1e890158ce35caa截图.png)

![](images/WEBRESOURCE3b15c54acd72d24fc9c2f0087854dbaa截图.png)

不然会这样

![](images/WEBRESOURCEa281a97772340bd04cc2f601ff3fba54截图.png)

（3）、部署： 

![](images/WEBRESOURCEfdd59eb4bd40d7357fb691400f3fb0df截图.png)

294、 地图定位（ ip获取范围（省市区），再用 region城市 限制范围 调用获取搜索关键字提示 达到效果 ）

295、 vue-cli 改造  nuxt  

vue 中的 **全局变量 **

![](images/WEBRESOURCEb9dea659f35208ffbd1fa9bf19b77dab截图.png)

296、 数据埋点 - 访问数据来源

【直接访问】：直接输入网站 url（网址） 进行访问

【搜索引擎】：关键字搜索跳转

【外部链接】：通过别人的网址跳转（带来源标识 from / source）

![](images/WEBRESOURCE1b6afa19c63f0c10eb9ccf6eddfae2fa截图.png)

297、跨域解决： 浏览器会先发一个 option 预请求, 不行就报错了

（1）、jsonp 发起标签 script请求，利用script标签的src不受同源的限制

（2）、vue.config.js / webpack 配置文件 修改 devServer 的 proxy 的指向

（3）、（服务端 / node 端）nginx 的 配置文件修改

**nginx:－－－－－－－  负载均衡 / 反向代理 / 动静分离 / http服务器  **（高性能 / 轻量级）

负载均衡： nginx代理服务器 转发请求到各个服务器（每个服务器都有各自的端口号唯一识别），利用空闲的服务器协调工作，做到资源利用最大化

![](images/WEBRESOURCE4251187738d865b1558da430c6de6c42截图.png)

![](images/WEBRESOURCE54dbb31610579662f3cce160fe430de4截图.png)

动静分离： nginx将 静态资源 和 动态资源 进行分离，给对应的服务器去解析，降低单个服务器的压力，加速解析

![](images/WEBRESOURCE908a2762c98eac00d8a7518a737a742f截图.png)

**模块配置：**

![](images/WEBRESOURCEf318aa360d129ce6719fe69c0c0638d2截图.png)

**配置文件 xxx.config**

![](images/WEBRESOURCE2f44b139c9358611dcb5a7c90673113c截图.png)

![](images/WEBRESOURCEc4f5c994fce0ddf95a205270da9e85ae截图.png)

**工程化 CI/CD **：  **项目部署和自动化打包**

**流程：**

1、服务器安装 jenkins，然后配置一个项目

![](images/WEBRESOURCEd5eabb4036ea9400ebe606fc8b0345ed截图.png)

2、然后构建规则，轮询配置

![](images/WEBRESOURCEed14a559bfbd65edf1b3b5aeef341fcf截图.png)

3、然后构建，配置构建命令，然后ssh到服务器指定的目录中

![](images/WEBRESOURCE66275a917f3e04e5b021f8a29e39a810截图.png)

4、接着服务器安装nginx，然后配置 location 的 root 为上面的 remote directory，然后完事。就能享受 jenkins 自动化部署，直接访问就能看到新的代码变化了。

![](images/WEBRESOURCE64c65f018d10fc191424834f400615b0截图.png)

298、   v-html 的 本质就是 设置元素原生的innerHTML

dom 的 innerHTML ： 选所有子元素进行显示 （子元素包括标签在内的字符串形式） : --   文本带标签的显示为带标签后的样式，普通字符串就显示普通字符串内容

![](images/WEBRESOURCE5c904b34b87a6953303555e635b87c43截图.png)

dom 的 innerText ： 选所有子元素的**所有文字**进行显示 （字符串像是）		       : --   文本是什么就显示什么（不认标签的）

![](images/WEBRESOURCEa419e76de758c62ed5aee6016eabeb68截图.png)

![](images/WEBRESOURCE458a2f97a0d70b891cdf540f51165e5c截图.png)

299、markdown  语法  中的 **锚点定位**

**（1）、****[ ****text content ...**** ]( #myname**** ****) ****   ------    <span ****id="myname"****>text content ...</span>**

**（2）、<a href="#anchor"> text content... </a>    -----   <span id="anchor">the anchor target</span>**

![](images/WEBRESOURCE57de595415e6f26cad4ea372c0e24738截图.png)

![](images/WEBRESOURCE1a8ead51e8b6613462508a6975bac6da截图.png)

**（3）、[[toc]]    ---    ****table of contents**

![](images/WEBRESOURCE824d22a566992f312faec27e9c712384截图.png)

**showndown   ---   **用 JavaScript 编写的双向 Markdown 到 HTML 到 Markdown 转换器

**showndown-toc  ----   [toc] 语法 生成 导航锚点定位**

1. 会把文章中所有的 heading 信息，通过闭包的形式传达到上层域

1. markdown 中写 [toc]，即可生成 toc 到 markdown 的相应位置中

一般的md编辑器（vue的 v-md-editor）就有集成了。使用的时候直接配置就行了。

![](images/WEBRESOURCEeb3f64458aa7f82778c3cb7a72950b51截图.png)

![](images/WEBRESOURCEbae1cc58155bf88fcc2ff5c15f8fb30e截图.png)

300、B站弹幕不遮挡人脸？ 

AI生成图片，然后给图片设置 -webkit-mask-image: url(xxx) 属性，文字 绝对定位， 图片相对定位。

![](images/WEBRESOURCE09bf5a2c902937486958aa54f604c74b截图.png)

![](images/WEBRESOURCE47df39de4f8d35433e7dc8b1ea0081a6截图.png)

![](images/WEBRESOURCEf57c195c3945638e13dce66feb7437ee截图.png)

**mask-image** [CSS](https://developer.mozilla.org/zh-CN/docs/Web/CSS)属性用于设置元素上遮罩层的图像。

排查问题，遇到报错

哪种情况适合哪种

排除法

打印日志

猜测法（猜哪里最可能出问题，找哪里）

debugger

插件调试法

看缓存数据（storage / vuex）

看源码---调试

有的时候代码没发现出问题，可能不是代码本身的问题  **（有可能受限于不同浏览器的各种策略，导致有的浏览器正常 有的设备上显示不正常）**

**换个思路，可能是载体做了限制 源码没问题**

或者是 api 不同浏览器做了限制，需要换种实现方法 来兼容 不同的设备 ！！

**onLoad      只在页面加载的时候更新一次**

**onShow     每次页面更新的时候都会调用**

**index > -1**  （已经有if ... else ... 判断在了）   就相当于   **index > -1 ？ true :  false**   

代码优化