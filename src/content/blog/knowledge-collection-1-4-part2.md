---
title: "前端开发知识点大杂烩 1-4 第二部分（256-300条）"
description: "涵盖 Vuex 状态管理、Flexbox 布局、Promise 与异步编程、TypeScript、Vite 配置、浏览器 API 及性能优化等前端核心知识"
date: "2021-10-20"
lang: "zh-CN"
category: "tech"
tag: "frontend"
tags: ["Vuex", "Flexbox", "Promise", "TypeScript", "Vite", "性能优化", "浏览器API"]
featured: true
---

# 前端开发知识点大杂烩 1-4 第二部分（256-300条）

> 这是知识点大杂烩系列的第四部分，内容涵盖状态管理、异步编程、TypeScript、构建工具以及性能优化等多个方面。

## 256. Vuex `mapState` 与 `mapGetters`

`mapState` 和 `mapGetters` 是 Vuex 提供的辅助函数，用于将 store 中的 state 和 getters 映射到组件的计算属性 (computed) 中，简化代码。

```javascript
import { mapGetters, mapState } from 'vuex'

export default {
  computed: {
    // 使用对象展开运算符将它们混合到计算属性中
    ...mapState(['user', 'count']),
    ...mapGetters(['isLoggedIn', 'cartTotal'])
  }
}
```

![mapState helper](./images/WEBRESOURCE026f1ff90d8df74440effaecaa676d94截图.png)

## 257. Vuex `mapMutations` 与 `mapActions`

`mapMutations` 和 `mapActions` 类似，用于将 store 中的 mutations 和 actions 映射到组件的 methods 中。

```javascript
import { mapActions, mapMutations } from 'vuex'

export default {
  methods: {
    ...mapMutations(['increment', 'setUser']),
    ...mapActions(['login', 'fetchProducts'])
  }
}
```

![mapActions helper](./images/WEBRESOURCE01eff6f749c9ce9c5e25b85ad93c1021截图.png)

## 258. Flexbox `flex-grow`, `flex-shrink`, `flex-basis`

这三个属性是 `flex` 简写属性的分项：

- `flex-grow`: 定义项目的放大比例（默认为0），即存在剩余空间时如何分配。
- `flex-shrink`: 定义项目的缩小比例（默认为1），即空间不足时如何收缩。
- `flex-basis`: 定义在分配多余空间之前，项目占据的主轴空间（默认为auto）。

![flex properties](./images/WEBRESOURCE170bfe3aee6e239338b5629478d0042bimage.png)

## 259. CSS Grid `grid-template-areas`

`grid-template-areas` 属性用于定义一个网格布局的区域，通过为网格项指定名称，可以直观地控制布局结构。

```css
.container {
  display: grid;
  grid-template-areas:
    'header header header'
    'nav    main   aside'
    'footer footer footer';
}
.header {
  grid-area: header;
}
/* ... */
```

![grid-template-areas](./images/WEBRESOURCE173f7ba32872bd1d04b2f4e2c2357d84截图.png)

## 260. Promise.allSettled()

`Promise.allSettled()` 接收一个 Promise 数组，当所有 Promise 都已经敲定（settled），无论是 fulfilled 还是 rejected，它都会返回一个 Promise，该 Promise 解析为一个描述每个 Promise 结果的对象数组。

**与 `Promise.all()` 的区别**：`Promise.all()` 在任何一个 Promise 被 reject 时就会立即 reject。

![Promise.allSettled](./images/WEBRESOURCE16a7e0fff26b150ac10c347f0d1a2918截图.png)

## 261. Optional Chaining (`?.`)

可选链操作符 (`?.`) 允许读取位于连接对象链深处的属性的值，而不必明确验证链中的每个引用是否有效。如果引用为 `null` 或 `undefined`，表达式会短路并返回 `undefined`。

```javascript
const user = {
  // address is missing
}
const street = user?.address?.street // Returns undefined instead of throwing an error
```

![Optional Chaining](./images/WEBRESOURCE15e98ea6d8d56e78bbdbd73db02acbdd截图.png)

## 262. Nullish Coalescing Operator (`??`)

空值合并操作符 (`??`) 是一个逻辑操作符，当左侧的操作数为 `null` 或 `undefined` 时，返回其右侧操作数，否则返回左侧操作数。

**与 `||` 的区别**：`||` 会在左侧为任何“假值”（如 `0`, `''`, `false`）时返回右侧，而 `??` 只对 `null` 和 `undefined` 生效。

```javascript
const count = 0
const finalCount = count ?? 10 // finalCount is 0
const finalCountWithOr = count || 10 // finalCountWithOr is 10
```

![Nullish Coalescing](./images/WEBRESOURCE15cc5e322494b0deddd28f828e581784截图.png)

## 263. TypeScript `interface` vs `type`

- `interface`: 只能用于描述对象或类的结构。可以被 `implements` 和 `extends`。同名 `interface` 会自动合并。
- `type`: 功能更强大，可以为任何类型创建别名，包括原始类型、联合类型、元组等。

**选择**：如果定义对象或类的形状，优先使用 `interface`；如果需要联合类型、交叉类型等高级功能，使用 `type`。

![interface vs type](./images/WEBRESOURCE14fb4b2e942e510a99fb3dd6a73b8be1截图.png)

## 264. TypeScript `enum`

`enum` (枚举) 允许我们为一组数值定义友好的名字。

```typescript
enum Direction {
  Up, // 0
  Down, // 1
  Left, // 2
  Right // 3
}
```

![TypeScript enum](./images/WEBRESOURCE147ea7064365ed4fe8f9d269dcb13992截图.png)

## 265. TypeScript `Utility Types`

TypeScript 内置了一些工具类型，用于简化常见的类型转换，如：

- `Partial<T>`: 将 `T` 的所有属性变为可选。
- `Readonly<T>`: 将 `T` 的所有属性变为只读。
- `Pick<T, K>`: 从 `T` 中选择一组属性 `K` 来构造一个新类型。
- `Omit<T, K>`: 从 `T` 中移除一组属性 `K` 来构造一个新类型。

![Utility Types](./images/WEBRESOURCE141415efd3f899f32cbdbb2d32045d78截图.png)

## 266. Vite 插件系统

Vite 的插件系统是其生态的核心。一个 Vite 插件是一个遵循特定接口的对象，可以用于扩展 Vite 的功能，例如转换代码、添加自定义解析规则等。它兼容 Rollup 插件接口。

![Vite plugins](./images/WEBRESOURCE13b8a4779e551cae88b8b4e475c08ee0截图.png)

## 267. Vite `define` 配置

`define` 配置项用于定义全局常量，在开发和构建时会被静态替换。

**`vite.config.js`**

```javascript
export default defineConfig({
  define: {
    __APP_VERSION__: JSON.stringify('v1.0.0')
  }
})
```

![Vite define](./images/WEBRESOURCE12b605a1ec98fc2c477b69ff283f887c截图.png)

## 268. 浏览器 `Intersection Observer API`

这个 API 提供了一种异步观察目标元素与其祖先元素或顶级文档视窗 (viewport) 交叉状态变化的方法。常用于实现图片懒加载、无限滚动等功能，性能远高于监听 `scroll` 事件。

![Intersection Observer](./images/WEBRESOURCE1296ff627d8a7122f090750bdf3fb26aimage.png)

## 269. 浏览器 `Resize Observer API`

`ResizeObserver` 接口可以监视一个元素的尺寸变化。当元素的 content rect 发生变化时，会触发回调函数。

![Resize Observer](./images/WEBRESOURCE1293accabeed2e5f76d8ee611b30fba8截图.png)

## 270. Web Workers

Web Worker 为 Web 内容在后台线程中运行脚本提供了一种简单的方法。线程可以执行任务而不干扰用户界面。一个 worker 可以通过 `postMessage()` 方法发送消息给创建它的 JavaScript 代码，并通过 `onmessage` 事件处理程序接收消息。

![Web Workers](./images/WEBRESOURCE11c5f030d960d1be9d8df416162fe239截图.png)

## 271. 性能优化：Tree Shaking

Tree Shaking 是一种通过移除 JavaScript 上下文中未引用的代码来优化代码体积的技术。它依赖于 ES2015 模块语法的静态结构（`import` 和 `export`）。Webpack 和 Rollup/Vite 都支持 Tree Shaking。

![Tree Shaking](./images/WEBRESOURCE18d93c3b7ee372cfe935164ddf818741image.png)

## 272. 性能优化：Code Splitting

代码分割是另一种减小首次加载体积的技术。它将代码分割成多个 chunk，这些 chunk 可以在需要时按需加载。Vue Router 的路由懒加载就是代码分割的一个典型应用。

```javascript
const UserProfile = () => import('./components/UserProfile.vue')
```

![Code Splitting](./images/WEBRESOURCEcde11397aae4ffee37cde02fbf5dded截图.png)

## 273. `localStorage` vs `sessionStorage` vs `Cookie`

- **`Cookie`**: 体积小 (4KB)，每次 HTTP 请求都会被携带到服务器，可设置过期时间。
- **`localStorage`**: 体积大 (5-10MB)，数据永久存储，除非手动清除。
- **`sessionStorage`**: 体积与 `localStorage` 相同，但数据只在当前会话（标签页）有效，关闭标签页后数据被清除。

![Storage comparison](./images/WEBRESOURCE0e9ca838edc13f460235b16c34eb8866截图.png)

## 274. `box-sizing: border-box`

`box-sizing: border-box` 改变了 CSS 盒模型的计算方式。元素的 `width` 和 `height` 属性将包括 `padding` 和 `border`，而不仅仅是内容区域。这使得布局更加直观和可预测。

![border-box](./images/WEBRESOURCE041b9903e35a3da50e9914a5bef88dab截图.png)

## 275. `Object.freeze()`

`Object.freeze()` 方法可以“冻结”一个对象。一个被冻结的对象不能再被修改：不能添加新属性，不能删除已有属性，不能修改已有属性的可枚举性、可配置性、可写性，以及不能修改已有属性的值。

在 Vue 中，可以使用 `Object.freeze()` 来处理纯展示的大型数据，阻止 Vue 对其进行响应式处理，从而优化性能。

![Object.freeze](./images/WEBRESOURCE13dd9a073681b2e11a0934cd870b6432截图.png)

## 276. `window.requestAnimationFrame()`

`requestAnimationFrame` 告诉浏览器你希望执行一个动画，并请求浏览器在下一次重绘之前调用指定的回调函数来更新动画。这是实现高性能、流畅 Web 动画的首选方法。

![requestAnimationFrame](./images/WEBRESOURCE0831419b4ebab5a1dc48845a7b369634截图.png)

## 277. CSS 变量 (Custom Properties)

CSS 变量允许我们在 CSS 中定义可重用的值。通过 `--` 前缀来声明一个变量，并通过 `var()` 函数来使用它。

```css
:root {
  --main-bg-color: coral;
}

body {
  background-color: var(--main-bg-color);
}
```

![CSS Variables](./images/WEBRESOURCE0cde11397aae4ffee37cde02fbf5dded截图.png)

## 278. `Array.prototype.reduce()`

`reduce()` 方法对数组中的每个元素执行一个由您提供的 reducer 函数(升序执行)，将其结果汇总为单个返回值。

```javascript
const array1 = [1, 2, 3, 4]
const sum = array1.reduce((accumulator, currentValue) => accumulator + currentValue, 0)
// sum is 10
```

![Array.reduce](./images/WEBRESOURCE0db89a3d6a1f8f91d52e111458fb1853截图.png)

## 279. 事件委托 (Event Delegation)

事件委托是一种利用事件冒泡的技巧，只在父元素上指定一个事件监听器，来管理所有子元素的事件。这比为每个子元素单独绑定监听器更高效。

![Event Delegation](./images/WEBRESOURCE0da5bf8ffe1b6b25167ca8b82cf550af截图.png)

## 280. `<img>` `srcset` 和 `sizes` 属性

这两个属性用于实现响应式图片，让浏览器根据不同的屏幕尺寸和分辨率加载最合适的图片资源。

- `srcset`: 提供一个以逗号分隔的图片 URL 列表，以及它们的宽度描述符 (e.g., `image-480w.jpg 480w`)。
- `sizes`: 定义图片在不同布局下的尺寸 (e.g., `(max-width: 600px) 480px, 800px`)。

![srcset and sizes](./images/WEBRESOURCE0d7f1660edbdfdbf6116ab3bab645626截图.png)

## 281. `Array.from()`

`Array.from()` 方法对一个类似数组或可迭代对象创建一个新的，浅拷贝的数组实例。

```javascript
// 从 Set 创建数组
const s = new Set(['foo', 'bar'])
Array.from(s) // ["foo", "bar"]

// 从 NodeList 创建数组
const divs = document.querySelectorAll('div')
const divArray = Array.from(divs)
```

![Array.from](./images/WEBRESOURCE0d1cbef9803a923c725de46c587cb52b截图.png)

## 282. `Proxy` 对象

`Proxy` 对象用于创建一个对象的代理，从而实现基本操作的拦截和自定义（如属性查找、赋值、枚举、函数调用等）。Vue 3 的响应式系统就是基于 `Proxy` 实现的。

![Proxy object](./images/WEBRESOURCE0b5d45cda6dab183171a95364a3595a9截图.png)

## 283. `Reflect` 对象

`Reflect` 是一个内置的对象，它提供拦截 JavaScript 操作的方法。这些方法与 `Proxy` 的处理器方法相同。`Reflect` 不是一个函数对象，因此它是不可构造的。

![Reflect object](./images/WEBRESOURCE0b1d80ef10044a6a1d53130633ba637b截图.png)

## 284. `WeakSet` 和 `WeakMap`

- `WeakSet`: 成员只能是对象，且是弱引用。
- `WeakMap`: 键只能是对象，且是弱引用。

“弱引用”意味着如果一个对象没有其他强引用指向它，垃圾回收机制就可以回收它，即使它仍然存在于 `WeakSet` 或 `WeakMap` 中。这对于避免内存泄漏非常有用。

![WeakSet and WeakMap](./images/WEBRESOURCE0ac92fa3ee4c76a82d750ee9ada4a5d1截图.png)

## 285. `BigInt`

`BigInt` 是一种新的数字类型，可以用任意精度表示整数。使用 `BigInt` 可以安全地存储和操作大整数，即使这个数已经超出了 `Number` 能够表示的安全整数范围。

```javascript
const a = 9007199254740991n
const b = BigInt(9007199254740991)
```

![BigInt](./images/WEBRESOURCE0a347b8da64a1c6ca9290b8f7cf41066截图.png)

## 286. `globalThis`

`globalThis` 提供了一个标准的方式来获取不同环境下的全局 `this` 对象（例如，浏览器中的 `window`，Node.js 中的 `global`）。

![globalThis](./images/WEBRESOURCE0a1e6087a5a4e961d841d82a428aa3eb截图.png)

## 287. `for...in` vs `for...of`

- `for...in`: 遍历对象的可枚举属性（包括原型链上的）。
- `for...of`: 遍历可迭代对象（如 `Array`, `Map`, `Set`, `String` 等）的值。

![for...in vs for...of](./images/WEBRESOURCE09bf5a2c902937486958aa54f604c74b截图.png)

## 288. `Set` 对象

`Set` 对象允许你存储任何类型的唯一值，无论是原始值或者是对象引用。

```javascript
const mySet = new Set([1, 1, 2, 3, 3]) // {1, 2, 3}
```

常用于数组去重。

![Set object](./images/WEBRESOURCE08af773c7fd7fa3e41b7d269e1a56cc8截图.png)

## 289. `Map` 对象

`Map` 对象保存键值对，并且能够记住键的原始插入顺序。任何值（对象或者原始值）都可以作为一个键或一个值。

![Map object](./images/WEBRESOURCE08333719a0969c1717e45d9e7591cc8f截图.png)

## 290. `Symbol.iterator`

`Symbol.iterator` 为每一个对象定义了默认的迭代器。该迭代器可以被 `for...of` 循环使用。

![Symbol.iterator](./images/WEBRESOURCE078630f400b8ec73f8c0ee3546ce2a61截图.png)

## 291. Generator 函数

Generator 函数是一种可以暂停和恢复执行的特殊函数。通过 `function*` 语法定义，并使用 `yield` 关键字来暂停。

![Generator function](./images/WEBRESOURCE072e3d84fe9bd1a81d67d0f07e94eb4b截图.png)

## 292. `async/await`

`async/await` 是基于 Promise 的语法糖，用于以同步的方式编写异步代码，使其更易于阅读和维护。

![async/await](./images/WEBRESOURCE070e34615dee614f033df590d2880bf5截图.png)

## 293. `class` 语法

ES6 引入了 `class` 关键字，作为创建对象和处理继承的语法糖。它底层仍然是基于原型继承。

![class syntax](./images/WEBRESOURCE06ef908e7438e4e8b9df512eb96aabc6截图.png)

## 294. 模块化 (`import`/`export`)

ES6 模块化 (`import`/`export`) 提供了在不同 JavaScript 文件之间共享代码的标准方式。

![ES6 Modules](./images/WEBRESOURCE06a3602af065ca00895e4b085e19edb8截图.png)

## 295. 箭头函数

箭头函数 (`=>`) 提供了更简洁的函数写法，并且不绑定自己的 `this`，它会捕获其所在上下文的 `this` 值。

![Arrow function](./images/WEBRESOURCE066c7022e7b8d62a643d382a8a09b76a截图.png)

## 296. 模板字符串

模板字符串使用反引号 (`` ` ``) 来定义，可以内嵌表达式 (`${expression}`) 和跨越多行。

![Template literals](./images/WEBRESOURCE04e021660333149e33f28a7c83816669image.png)

## 297. 解构赋值

解构赋值语法是一种可以从数组或对象中提取值，对变量进行赋值的 JavaScript 表达式。

![Destructuring assignment](./images/WEBRESOURCE04ac208f3f881192b5525f70dba395a7image.png)

## 298. 默认参数

函数参数可以设置默认值，当函数调用时没有提供参数或参数值为 `undefined` 时，会使用默认值。

![Default parameters](./images/WEBRESOURCE026f1ff90d8df74440effaecaa676d94截图.png)

## 299. 展开语法 (`...`)

展开语法 (`...`) 允许一个可迭代对象（如数组）在期望零个或多个参数（用于函数调用）或元素（用于数组字面量）的位置被展开。

![Spread syntax](./images/WEBRESOURCE01eff6f749c9ce9c5e25b85ad93c1021截图.png)

## 300. `let` 和 `const`

`let` 和 `const` 是 ES6 引入的新的变量声明方式，它们都具有块级作用域。

- `let`: 声明一个块级作用域的局部变量，可以选择性地初始化一个值。
- `const`: 声明一个块级作用域的只读常量。

![let and const](./images/WEBRESOURCE0161d6f430efbbb2da447b112618260c截图.png)
