---
title: "个人开发知识库（239-282）"
description: "代码提交规范、命名约定、Vue组件开发、JavaScript高级技巧、浏览器调试工具、前端工程化等深度技术内容"
date: "2023-09-11"
lang: "zh-CN"
category: "tech"
tag: "knowledge-base"
tags: ["Vue.js", "JavaScript", "Engineering", "DevOps", "Browser-Tools", "Frontend-Development"]
featured: true
---

## 239. 代码提交规范

代码要每天都提交一次！不要按功能完成再提交，确保 服务器能备份到最新的代码，确保本地不会因为硬盘坏了而丢失代码（即使是万分之一的概率）。

提交的时候只要确保基本的编译不会出错就可以了，只是提交到你的 feature 分支，不会影响到 master 分支的完整性的。 到时候一个功能开发完成了再通过测试后

再合并到主分支就可以了。

参考 **devOps** 的操作流程。

CI / CD 持续集成 / 持续部署（交付） continuous integration

continuous deployment（delivery）

## 240. 命名规范

命名： 数组： menu**s** menu**List** Nodes records

对象： **data** menu**Info** menu**Dict**

Map: menu**Map**

Set: menu**Set**

## 241. img标签的 onerror 事件处理

用 img标签的 **onerror（@error）事件 **解决图片的src地址访问不到报错（或者图片裂开）的问题

![](../../assets/images/WEBRESOURCE97d00e128d245b745d0a906837e9cefa截图.png)

原生：

![](../../assets/images/WEBRESOURCEa59f38776a6183200d9317b907544a98截图.png)

![](../../assets/images/WEBRESOURCE66f11cc5f12a433ebfdbc409d23c684d截图.png)

VUE:

![](../../assets/images/WEBRESOURCEa7aab5083069e404223b697c244715da截图.png)

![](../../assets/images/WEBRESOURCE01eff6f749c9ce9c5e25b85ad93c1021截图.png)

## 242. 可拖拽的视口

![](../../assets/images/WEBRESOURCE141415efd3f899f32cbdbb2d32045d78截图.png)

cursor: col-resize;

## 243. 获取外部样式的方法

window.getComputedStyle( ele ) 获取该元素的所有外部样式对象 （有width属性 ......） // 返回值是 整数

ele.getBoundingClientRect( ).top / bottom / left / right 返回一个矩形对象，包含那4个属性 // 返回值是 精确 的 带小数点的

ele.offsetWidth / offsetHeight / offsetLeft / offsetTop // 返回的是 整数

![](../../assets/images/WEBRESOURCEdf6e491ebb185297a5f564eda9e8a3d5截图.png)

## 244. package.json 文件中的依赖类型区别

package.json 文件中的 dependencies（项目的依赖） 和 devDependencies（开发所需要的模块） 和 peerDependencies 的区别：

（1）、如果没有发布 npm 包， 那么依赖放在哪里没有区别；

（2）、为了规范，如果是一直使用的包（项目中会一直用，如 ant-design-vue、day.js等），放到 dependencies 中; （这里的依赖是一定会被下载的）

如果是开发时需要用到，上线后（线上环境）不会用到的，如 webpack、eslint、prettier、各种-loader、stylelint等...，放到 devDependencies 中

（3）、peerDependencies 解决核心库被下载多次，统一核心库的版本问题 （项目依赖了 vuex 和 vant 这两个子库，而这两个依赖又都同时依赖了 vue 这个框架。在字库中分别声明 peerDependencies 防止重复安装 vue）

![](../../assets/images/WEBRESOURCEc211f1a9c8579be708b19fd63cfe8238截图.png)

![](../../assets/images/WEBRESOURCE5e6d81d5ca397512a7cdf807720497a2截图.png)

（4）、peerDependenciesMeta 对 peerDependencies 的修饰， 增加一些可选项的配置

## 245. CSS 字体 font-weight 属性的跨平台差异

关于 css 字体 的一些学问 **font-weight** 属性在不同操作系统（常见win和mac）上的显示效果的不同

（0）、关于 win 和 mac 的默认字体 （不设置font-family属性，会自动读取操作系统的默认字体来显示的）

![](../../assets/images/WEBRESOURCE166d88b8229ee4226d547dc48abb063b截图.png)

（1）、win上 ：600为分界线。600往前一个效果，600往后一个效果

      mac上：每一个值显示的效果都不一样

![](../../assets/images/WEBRESOURCE5bdf8a6a725de89b82a4dcf80dbf49bc截图.png)

（2）、因为操作系统和浏览器的原因，字体在不同操作系统设备上显示的粗细会有所不同。这是 font-weight 这个属性存在的兼容性问题

![](../../assets/images/WEBRESOURCE949b061644d5ffe6968a8e87c5ca6a81截图.png)

（3）、使用 @font-face 引入自定义字体，font-family 使用该字体名称 （字体后缀名 .ttf .otf .fnt ）

![](../../assets/images/WEBRESOURCE5d9c4e9fbc8ebc9e94738d5b4814f81f截图.png)

![](../../assets/images/WEBRESOURCE3d513376b6dc1586dbf88cefc765c5d5截图.png)

![](../../assets/images/WEBRESOURCEde5d7be89a71a3021be0d7c803882a3c截图.png)

## 246. Vue 中多个组件使用 window.onresize 的问题

vue 中 多个组件使用 window.onresize，只有一个生效，导致其他的给覆盖。

![](../../assets/images/WEBRESOURCEe8b4bb46a90e055ff3fda485524efd02截图.png)

![](../../assets/images/WEBRESOURCEee606fc35d578068f19537e5e8665ed8截图.png)

## 247. JavaScript 引擎和编译器

js引擎（spiderMonkey、V8 ...） （js解析器） 和 js编译器 （babel （es6 转 es5，转化 jsx 语法，兼容旧版浏览器，ts语法）、 tsc （typescript compiler）、 swc （speedy web compiler））

![](../../assets/images/WEBRESOURCEe53b4fb6f87071e8bde0ab38e1c6cd1d截图.png)

## 248. CommonJS 和 ES Module 的区别

commonJS （服务端 node） 和 ES Module 的区别：

(1)、require 和 import 的

(2)、CommonJS 的思路就是将函数当作模块

## 249. JS的对象遍历是无序的

JS的对象遍历是无序的（因为对象本身就没有存储顺序）。但是数组是有的，有index索引，记录每个元素的顺序。

![](../../assets/images/WEBRESOURCE77d7c78acd977400a19abc6c57300756截图.png)

![](../../assets/images/WEBRESOURCEc0f698cf324163b7e8cc47b75a007dc8截图.png)

![](../../assets/images/WEBRESOURCEc2e1bb8bf07d8923b6f65ea8706c07e3截图.png)

![](../../assets/images/WEBRESOURCEdf5c03bc370ca631f23a9c0683ccf89c截图.png)

## 250. JS（V8）垃圾回收机制

JS（V8）垃圾回收机制： GC -----> **Garbage Collecton** **垃圾回收** 垃圾收集器 （自动回收机制） （C / C++ 就没有）

![](../../assets/images/WEBRESOURCE72fdad86372ecfad7f69b2902b44dce4截图.png)

标识无用变量方法（GC 跟踪每个变量是否有用，无用变量打上标记，回收其占用内存）： 标记清除 （V8） 和 引用计数

标记清除会导致内存空间分配不连续（内存碎片）、分配速度慢

![](../../assets/images/WEBRESOURCEd87407baac9d352a1e9398c26bb22079截图.png)

标记整理：

![](../../assets/images/WEBRESOURCE5b03b0228253c5fd3069a8e14736ec6c截图.png)

引用计数（ 跟踪记录每个值被引用的次数。

![](../../assets/images/WEBRESOURCE3347afb78f2ff8cc1650526b72db91fc截图.png)

）

![](../../assets/images/WEBRESOURCE0e84ac809a5bb9a28c5f92aa8bc95d0a截图.png)

上面 即 循环引用的 例子

![](../../assets/images/WEBRESOURCE007d5cfe1006ba29f9b0d58b8be5693f截图.png)

回收

## 251. Vue 动态绑定属性名

vue 动态绑定属性名

![](../../assets/images/WEBRESOURCEf1e0af80a815303beb08ccb7f564a8e8截图.png)

## 252. Vue 中 v-for 支持的类型

vue 中 v-for 支持的类型 （比原生的 for 屌很多的） in / of 这两个都可以，作用完全一样（跟原生是不一样的）

1、遍历 数组（字符串）

2、遍历对象 value - key - index

             （键值对索引）

![](../../assets/images/WEBRESOURCE30586ded7c557f02528c828312ed0c88截图.png)

![](../../assets/images/WEBRESOURCE90332e754d821609c3efdb52b63e2522截图.png)

3、遍历数字

4、遍历 itarable（可迭代）对象

**key 属性 ** ）

### **Vue3.x 新增 自动生成唯一的 key 值**

![](../../assets/images/WEBRESOURCE8f9dbff715267a723372b9332c2511bc截图.png)

### **Vue2.x 必须写key **

![](../../assets/images/WEBRESOURCE4edf34a6a02da628d225f71bf337402e截图.png)

VNode 虚拟节点

template中的每一个标签元素 vue 都会转化成 一个个的 VNode

template -> VNode -> 真实DOM

![](../../assets/images/WEBRESOURCE84da67a6f392a555ec3d5946e9174c39截图.png)

虚拟DOM ？ 可以干嘛？

（1）、做 diff 算法 （绑定 v-for 的 key）

（2）、跨平台。vue 可以写 PC Web / 移动端 / H5 / 小程序 / 甚至 桌面端 / 安卓 / IOS ...... 靠的是 VDOM 的 转换

![](../../assets/images/WEBRESOURCE2abf9f03ce3c628937fc78f6c7d38d34截图.png)

## 253. position: fixed 的定位特性

position: fixed; 一般都是相对于 屏幕视窗 （浏览器窗口） 来进行 定位 。（屏幕尺寸大小）

所以 设置 width 要动态计算 calc（）函数 除非

![](../../assets/images/WEBRESOURCE6d33b86a379c78b7bee94a8b522df13a截图.png)

## 254. Vue 组件库相关

Vue 组件库 相关

（1）、直接用

```javascript
main.js 文件
// 组件库
import Element from 'element-ui'

Vue.use(Element) // 使用组件库的所有组件（全部加载，不管用没用到）
```

（2）、lazy-use ** 按需引入**

```javascript
main.js 文件
// 组件库
import './lazy_use' // use lazy load components

lazy_use.js 文件
import Vue from 'vue'
import { Input, List, Dropdown, Tooltip ... } from 'ant-design-vue' // 按需引入

Vue.use(Input)
Vue.use(List)
Vue.use(Dropdown)
Vue.use(Tooltip)
...
```

如果仅仅是完成上面的操作，还是不能实现真正的按需加载的。

需要下载 babel-plugin-component 插件 并且 在 babel.config.js 文件中进行对应的插件配置（按需引入的组件库和按需引入的样式）

![](../../assets/images/WEBRESOURCE6929495e782ba924ace6263abc55d0ad截图.png)

![](../../assets/images/WEBRESOURCE8d62fc8dd95bf066fc1843674d8b9fbd截图.png)

以上，就可以做到按需引入了。

重写组件库样式：

1、建element-ui.scss文件在style文件夹中，在index.scss中@import引入，然后在 App.vue 文件的 style 中 @import 引入

![](../../assets/images/WEBRESOURCEcea01ab6b8562cd26e31893ce93c89fd截图.png)

![](../../assets/images/WEBRESOURCEe70fd8306645b8cab322369a0c8bda61截图.png)

![](../../assets/images/WEBRESOURCEa198f7db22bd2797d585e1c118802892截图.png)

2、建global.less文件重写 antdv 样式，在 main.js 中直接 import 该文件

![](../../assets/images/WEBRESOURCE2a0fddc163be903ef2329434656dd1a4截图.png)

这样就可以 了。

**element-ui 的 layout 布局 中的 响应式布局**

```xml
<el-col :lg="{span:'12-25'}"></el-col>
```

```xml
.el-col-lg-12-25 {
    width: 48%;
}
```

:lg="{span:'12-25'}" 和 .el-col-lg-12-25 =======> 即 el-col-lg-12-25 设置的 width 就是 :lg="{span:'12-25'}" 对应的宽度 （12-25 就是从第12列到第25列 的意思 ）

![](../../assets/images/WEBRESOURCE457599bab53401971afe485161862ec4截图.png)

## 255. property 和 attribute 的区别

property 和 attribute 的 区别 （类似 $attr 和 $props）

1、 **property**（如 **style,className,childNodes,classList ...**）

**（1）、** 指的是操作 DOM 的属性 像获取到DOM后设置 xxx.style.color、xxx.className、xxx.childNodes、xxx.firstChild .......

（2）、值只能是字符串（操作JS）

（3）、长属性名是驼峰写法

![](../../assets/images/WEBRESOURCE4b9332da1104d643130fc0cc1cdb9056截图.png)

2、 **attribute**（如 **id,class,src,title,alt ...**）

**（1）、** 指的是操作 HTML 的属性 像设置和获取DOM元素的属性 xxx.getAttribute('class')、xxx.setAttribute('title', 'cusTitle')

（2）、值可以是 数组/ 对象等（HTML）

（3）、属性名

![](../../assets/images/WEBRESOURCEad7de01eca9e34de97c7af868fc1797b截图.png)

![](../../assets/images/WEBRESOURCEab733e26e74cecc0d7415c9fac0be508截图.png)

e.g.

![](../../assets/images/WEBRESOURCE3cbfc8c2dc63d191cf78508d21f5ff1b截图.png)

![](../../assets/images/WEBRESOURCE9bf20989904ceb30a54b223b2d14ab1a截图.png)

![](../../assets/images/WEBRESOURCE4089be42d661990f2f7270c3cb7c4e28截图.png)

$listeners （vue3 deprecated）

![](../../assets/images/WEBRESOURCE8d0e3c1cbd82619cc3d84f01dceb5e49截图.png)

![](../../assets/images/WEBRESOURCEecef1504a7dd8197feb79c3431d706af截图.png)

v-bind="$attrs"  v-on="$listeners" $options ===> options API 整个 获取自定义属性

事件监听的具体使用：

父组件：

![](../../assets/images/WEBRESOURCEf95369162aa977929a8a4ae522e81fdc截图.png)

子组件：

![](../../assets/images/WEBRESOURCEa46fb5efdcfbc588aa054694fb9242ec截图.png)

这里的v-on="$listeners" 就相当于 继承了 父组件中的除了 @run 之外的所有事件监听了（也就是红框框出来的那些事件）

也相当于：

![](../../assets/images/WEBRESOURCEc4aff3e32d445fb22c7377a4d0a5fc20截图.png)

替代了 手动写$emit 事件 。

所以，这个 @run 在 父组件定义就行了。 在子组件这里再次定义的话就相当于 你执行了 两次 run 了。

![](../../assets/images/WEBRESOURCE37aa2edf540133907dbbf7709de010ed截图.png)

## 256. Vue 的 install

![](../../assets/images/WEBRESOURCE08333719a0969c1717e45d9e7591cc8f截图.png)

## 257. flex 和 grid 布局

flex （一维 / 单行） grid （ 二(多)维 / 多行 ）

**让最后一行列表左对齐**

(1)、grid

```javascript
 display: grid;
 justify-content: space-around;
 grid-template-columns: repeat(auto-fill, 30%);
```

![](../../assets/images/WEBRESOURCE6ae5f93f4e055bc325a2fb51f2dfb965截图.png)

```javascript
// 子元素
width: 30%;
height: 100px;
background: pink;
margin: 12px;
```

(1)、flex

1. 粗暴点，直接给最后一个 margin-right

2. 在后面套两个 <div></div><div></div> (空元素占位)

![](../../assets/images/WEBRESOURCE2b74f7b18b1d5a2806c6e4ae00772e91截图.png)

## 258. JSON Schema

JSON Schema

JSON : [JavaScript Object Notation](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON#javascript_object_notation) js对象（万物皆对象）注释

**JSON** 是一种语法，用来**序列化**对象、数组、数值、字符串、布尔值和 [null](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Operators/null) 。key 和 value 都是 字符串

**JSON Schema 是一套 JSON规范，用于校验JSON的工具。**(格式有点类似于AST？) 就是一份JSON文件的说明文档，叼一点，可以生成form表单（插件支持）和做数据（字段）校验

包含了「表单数据描述」和 「表单校验」功能。

基本格式：

![](../../assets/images/WEBRESOURCE2449656b67422bce6c6a1585ea4d5c58截图.png)

json格式是不支持注释的，所以可以用 json schema 来定义json，在json文件里加个description字段就可以了。title是定义标题的。

field widget 组件

语法： 定义了一系列的规则（关键字） ajv: JSON Schema 的校验器

组成：

#### **1、schema：** **用于描述表单数据的 JSON Schema**

![](../../assets/images/WEBRESOURCE365b1420adc78d6e4722dd26b90807a4截图.png)

![](../../assets/images/WEBRESOURCE3b3cb23f3801800fb6b153f434c51d46截图.png)

#### **2、ui-schema： （ui 也可直接配置在 schema 中）** **用于配置表单展示样式**

![](../../assets/images/WEBRESOURCE8029c07147f584f210d621bca9161386截图.png)

**3、error-schema ：**用于配置表单校验错误文案信息 \*\*

.......

关键字：

1、$schema 关键字：声明了针对哪个版本的JSON Schema标准编写模式。

2、$id 属性：为JSON Schema实例声明一个唯一标识符；声明了一个解析$ref 的URI时的基础URI

3、$ref 属性：引用同一个JSON Schema文件或其他JSON Schema文件中的JSON Schema实例片段。这个属性的值通常是一个相对或者绝对URI，# 符号后面的部分是JSON指针

4、dependencies 关键字：属性依赖：如果JSON实例拥有对应的属性名name，则也必须有name对应的propertyset数组内的所有属性名

5、definitions 关键字：这个关键字可以在JSON Schema实例中定义一个可以在这个JSON Schema实例中被引用的JSON子模式。

## 259. position 和 flex 的冲突

同一个盒子同时使用 position 和 flex 会产生 冲突 （flex居中 和 子元素 position: absolute 之间产生的冲突）， 给盒子外面再套一层 div，相对 div 定位

## 260. 声明式编程

声明式编程 （**函数式编程** 是 其中一种） 与之对应的是 命令式编程

![](../../assets/images/WEBRESOURCE39b12c6c985158acebe7ff8fb6215dae截图.png)

## 261. React

React

（1）、开发 web 页面

（2）、React Native 开发 移动端跨平台

（3）、React VR 开发 虚拟现实web应用

依赖包： react / react-dom（ 渲染到不同平台的依赖，比如 react web 和 react native ） / babel

vue只要数据更新,会自动进行render,视图上显示的是最新的数据。

而react默认不会自动执行render (可以是 html元素 或 组件--------类组件和函数式组件)的，要在数据更行后手动执行一下render拿到最新数据

script标签中加 type="text/babel" 为了能让 babel 解析 jsx 代码

函数的返回值默认是 undefined （没写 return 的话）

es6的 class 默认是 绑定的严格模式下的，所以 class 里的 this 如果原本是指向 window 的，会变成 undefined （使用 babel 转换的代码也是，默认都是开启严格模式）

## 262. 禁止移动端的触摸事件

禁止移动端的触摸事件（touch event），可以强制不显示浏览器的滚动条（比如 safari 会自动显示滚动条）

** touch-event: none;**

## 263. VUE_BASE_API 配置

VUE_BASE_API 在 .env等 配置文件 中 ：

（1）、完整的 url ： 那就是 **base_url **+ api文件 中的 ** request_url **

![](../../assets/images/WEBRESOURCE9be4cddb2b4ef5e1343b4d9cbd2fbb2e截图.png)

本地（开发）环境 和 测试环境 都是 这个的话，那访问的 api 就是

![](../../assets/images/WEBRESOURCE856496b4b0bf572220ba7edbbeb3c3f8截图.png)

生产环境 的 api

![](../../assets/images/WEBRESOURCEe807ef62eaf6bb7e3a5574ae94a44fb8截图.png)

![](../../assets/images/WEBRESOURCE4ccfdc3ff36a3c574969f92a61cc26cc截图.png)

（2）、单纯写 /api 这个是在 nginx.config 配置文件中进行 配置的。 （nginx是部署在服务器上面的，不是在后端代码中的。nginx 服务 配置项目）

![](../../assets/images/WEBRESOURCEd36db036d6fe42358d5baf3bbcb11f69截图.png)

1、如果是在 开发环境 中，那么访问的 baseUrl 就是 http://172.21.44.15:8080 + requestUrl 就是 /api/user/search

（如果没在vue.config.js文件中的devServer的proxy中设置 '/api' 代理的话，那就会报 404 not found 错误。要改VUE_BASE_API的）

2、如果是在 测试环境 中，那么访问的 baseUrl 就是 http://api-test.xd0760.com + requestUrl 就是 /api/user/search

3、如果是在 生产（正式）环境 中，那么访问的 baseUrl 就是 https://api.xd0760.com + requestUrl 就是 /api/user/search

同理，VUE_BASE_API_UPLOAD 也一样，设置 不同环境下的服务器存储文件（图片、视频等）的地址，如果是配置的 /uploads 的话，要注意对应环境，如果是测试环境 就会报错了

![](../../assets/images/WEBRESOURCE31ac36b2ba413cadeea833faf959e45c截图.png)

## 264. 随机生成颜色

随机生成颜色

![](../../assets/images/WEBRESOURCEdb71acf1ec8aa03a16009ad5c9593054截图.png)

const createRandomColor = () => `#${Math.random().toString(16).slice(-6)}`

## 265. 监听本地存储变化

原生方法 监听 本地存储 （localStorage）的变化：

window.addEventListener('storage', (event) => {

console.log('storage changed', event)

})

![](../../assets/images/WEBRESOURCE11c5f030d960d1be9d8df416162fe239截图.png)

![](../../assets/images/WEBRESOURCEff1435ff838c3b3669093bc002d0e340截图.png)

var orignalSetItem = localStorage.setItem

localStorage.setItem = function(key,newValue) {
var setItemEvent = new Event("setItemEvent")
setItemEvent.newValue = newValue
window.dispatchEvent(setItemEvent)
orignalSetItem.apply(this,arguments)
}

window.addEventListener("setItemEvent", function (e) {
alert(e.newValue)
})
localStorage.setItem("name","wang")

## 266. 修改 node_modules 代码

修改 node_modules 上的代码： 用 patch-package （依赖 git diff 实现的 patches 文件夹） 呀

注意: 要改动的包在 package.json 中必须声明确定的版本，不能有~或者^的前缀。

（1）、安装patch-package

```javascript
npm i patch-package --save-dev
```

（2）、修改完依赖后，运行patch-package创建patch文件 npx 执行 node_modules 文件夹 下面的 .bin 可执行文件

```javascript
npx patch-package 依赖名称
例子： npx patch-package element-ui
```

（3）、修改package.json的内容，在scripts中加入"postinstall": "patch-package"，这个是为了npm install的时候自动为依赖包打上我们改过的依赖

```javascript
 "scripts": {
　　...
　　"**postinstall**": "patch-package"
　　}
```

## 267. Vue 的各版本对比

vue 的 各版本 对比：

## 268. 编程英语

编程英语：

pseudo-element： 伪元素

![](../../assets/images/WEBRESOURCE6eb0ff03168d846bc57353e882583e95截图.png)

locale： 特定语言环境的（当地的） 'zh-CN' 'en-US' （'ar-EG' 阿拉伯语言） to**Locale**String() to**Locale**LowerCase()

SDK： **software development kit **软件开发工具包

accessibility： 无障碍 （a11y） ----视 / 听 / 行动 / 理解

patch： 打补丁

bundler： 打包（工具）（webpack / browserify）

chunk： 块

bundle： 束

vendor： 供应商（第三方库）

我们直接写出来的是 module，webpack 处理时是 chunk，最后生成浏览器可以直接运行的 bundle。（不是多少个 chunk 就会对应生成多少个 bundle ， （虽然大部分情况下会是一一对应） ）

invoke： 调用 （函数调用等）

revoke： 撤销

implicit： 隐式的...

explicit： 显式的...

hoisting： 变量提升 (var关键字声明才会有)

indicate： 表明，显示

anonymous： 匿名的...

nested： 嵌套的..

polygon： 多边形

immutable： 不可变的 （immer 一直如此 immer.js）

coupon： 优惠券

trie：字典树

traverse： 遍历

linked list： 链表

expanse ： 花费 （消耗 expansive 贵的）

manifest： 清单文件（）

**fallback： **兜底

estimate： 估计放

diagnostics： 诊断

concurrent： 并发

shims： 垫片 shims.vue.ts

Polyfill： 填充器

**关于符号：（prettie）**

semi： 末尾加不加分号 （"false" "true"）一行的结束

semicolon： 分号

semi-spacing： 强制分号间隔；分号前后是否空格

brackets： 括号

**comma**： 逗号

**trailingComma**：多行输入尾部加不加逗号

![](../../assets/images/WEBRESOURCE21742d03a98500f6921a6fd1e6574696截图.png)

单引号： singleQoute

双引号： doubleQoute

wrap： 换行 （nowrap --- 一行显示 white-space 只有一行文字的时候） wrapper： 包装器

## 269. 浏览器调试技巧

浏览器调试技巧：

**console.table** / **console.dir** / **console.clear()** 代替 console.table / dir / **console.clear()**

**监听事件**：像 vue 一样 在方法中打印 当前元素的DOM对象事件 (e) / ($event) ? 使用 **monitorEvents** 这个 api

![](../../assets/images/WEBRESOURCEf04b1efa45f243c72185ceb9255f1983截图.png)

![](../../assets/images/WEBRESOURCEb83c9e47767d63073683ffc4ffb83636截图.png)

**monitor** 相当于 vue 中的 watch 了，对函数做监听？？ 取消就 unmonitor 了

## 270. 浏览器性能监控

浏览器性能监控：

**performance.now()** 获取高精度时间戳

**performance.mark()** 和 **performance.measure()** 标记和测量性能

![](../../assets/images/WEBRESOURCEf04b1efa45f243c72185ceb9255f1983截图.png)

## 271. 浏览器存储

浏览器存储：

**localStorage** 持久化存储，除非手动清除

**sessionStorage** 会话存储，关闭标签页就清除

**IndexedDB** 大容量客户端存储

**Cookie** 小容量存储，会自动发送到服务器

![](../../assets/images/WEBRESOURCEb83c9e47767d63073683ffc4ffb83636截图.png)

## 272. 浏览器事件循环

浏览器事件循环：

**宏任务**：setTimeout、setInterval、I/O、UI 渲染

**微任务**：Promise.then、MutationObserver、queueMicrotask

执行顺序：同步代码 → 微任务 → 宏任务

![](../../assets/images/WEBRESOURCEf04b1efa45f243c72185ceb9255f1983截图.png)

## 273. 前端工程化

前端工程化：

**模块化**：CommonJS、ES Module、AMD、UMD

**构建工具**：Webpack、Vite、Rollup、Parcel

**包管理**：npm、yarn、pnpm

**代码规范**：ESLint、Prettier、Stylelint

**版本控制**：Git、SVN

**CI/CD**：Jenkins、GitHub Actions、GitLab CI

## 274. 前端性能优化

前端性能优化：

**加载优化**：懒加载、预加载、代码分割、CDN

**渲染优化**：虚拟滚动、防抖节流、避免重排重绘

**缓存优化**：HTTP缓存、Service Worker、本地存储

**网络优化**：HTTP/2、压缩、合并请求

## 275. 前端安全

前端安全：

**XSS**：跨站脚本攻击，防范措施包括输入验证、输出编码、CSP

**CSRF**：跨站请求伪造，防范措施包括Token验证、SameSite Cookie

**点击劫持**：通过iframe嵌套页面，防范措施包括X-Frame-Options

**SQL注入**：通过输入恶意SQL代码，防范措施包括参数化查询

## 276. 前端测试

前端测试：

**单元测试**：Jest、Mocha、Jasmine

**集成测试**：Testing Library、Enzyme

**端到端测试**：Cypress、Playwright、Puppeteer

**性能测试**：Lighthouse、WebPageTest

## 277. 前端监控

前端监控：

**错误监控**：Sentry、Bugsnag、LogRocket

**性能监控**：Google Analytics、百度统计

**用户行为监控**：热力图、用户录屏

**实时监控**：APM工具、自定义埋点

## 278. 前端架构

前端架构：

**MVC**：Model-View-Controller

**MVP**：Model-View-Presenter

**MVVM**：Model-View-ViewModel

**组件化**：可复用、可维护、可测试

**微前端**：qiankun、single-spa、Module Federation

## 279. 前端趋势

前端趋势：

**框架发展**：React 18、Vue 3、Svelte、Solid.js

**构建工具**：Vite、esbuild、SWC

**CSS发展**：CSS-in-JS、Tailwind CSS、CSS Modules

**TypeScript**：类型安全、开发体验提升

**WebAssembly**：高性能计算、跨语言开发

## 280. 移动端开发

移动端开发：

**响应式设计**：媒体查询、弹性布局、流式布局

**移动端适配**：viewport、rem、vw/vh

**触摸事件**：touchstart、touchmove、touchend

**性能优化**：减少重排重绘、优化动画、懒加载

**PWA**：Service Worker、Web App Manifest、离线缓存

## 281. 跨平台开发

跨平台开发：

**Hybrid App**：Cordova、PhoneGap、Ionic

**React Native**：原生性能、热更新、跨平台

**Flutter**：Dart语言、高性能渲染、丰富组件

**Electron**：桌面应用、Web技术栈、跨平台

**小程序**：微信、支付宝、百度、字节跳动

## 282. B站弹幕不遮挡人脸

B站弹幕不遮挡人脸？

AI生成图片，然后给图片设置 -webkit-mask-image: url(xxx) 属性，文字 绝对定位， 图片相对定位。

![](../../assets/images/WEBRESOURCE09bf5a2c902937486958aa54f604c74b截图.png)

![](../../assets/images/WEBRESOURCEmask-image-demo1.png)

![](../../assets/images/WEBRESOURCEmask-image-demo2.png)

mask-image CSS属性用于设置元素上遮罩层的图像。
